#!/usr/bin/env python3
"""
Verdure Application Startup Script
"""

import os
import sys
import subprocess
from pathlib import Path

def check_requirements():
    """Check if all requirements are met"""
    print("🔍 Checking requirements...")
    
    # Check if .env file exists
    if not os.path.exists('.env'):
        print("❌ .env file not found!")
        print("📋 Please copy .env.template to .env and configure your settings")
        return False
    
    # Check if requirements are installed
    try:
        import flask
        import sqlalchemy
        import requests
        import aiohttp
        import chromadb
        print("✅ Python dependencies installed")
    except ImportError as e:
        print(f"❌ Missing Python dependency: {e}")
        print("📦 Please install requirements: pip install -r requirements.txt")
        return False
    
    return True

def check_ollama():
    """Check if Ollama is running"""
    print("🤖 Checking Ollama service...")
    try:
        import requests
        response = requests.get('http://localhost:11434/api/version', timeout=5)
        if response.status_code == 200:
            print("✅ Ollama is running")
            return True
    except Exception:
        pass
    
    print("⚠️  Ollama not detected at localhost:11434")
    print("📖 Please install and start Ollama: https://ollama.ai/")
    print("🔧 Then run: ollama pull nomic-embed-text")
    return False

def setup_database():
    """Setup database tables"""
    print("🗄️  Setting up database...")
    try:
        from app import app, db
        with app.app_context():
            db.create_all()
        print("✅ Database initialized")
        return True
    except Exception as e:
        print(f"❌ Database setup failed: {e}")
        return False

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")
    directories = ['uploads', 'chroma_db', 'logs']
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ Directories created")

def main():
    """Main startup function"""
    print("🌱 Starting Verdure - AI Student Assistant")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        sys.exit(1)
    
    # Create directories
    create_directories()
    
    # Check Ollama (optional for basic functionality)
    ollama_running = check_ollama()
    
    # Setup database
    if not setup_database():
        sys.exit(1)
    
    print("\n🚀 Starting Flask application...")
    print("📱 Open your browser to: http://localhost:5000")
    print("⏹️  Press Ctrl+C to stop")
    
    if not ollama_running:
        print("\n⚠️  Note: Semantic search will be limited without Ollama")
    
    print("=" * 50)
    
    # Start Flask app
    try:
        from app import app
        app.run(debug=True, host='0.0.0.0', port=5000)
    except KeyboardInterrupt:
        print("\n👋 Shutting down Verdure...")
    except Exception as e:
        print(f"\n❌ Application error: {e}")
        sys.exit(1)

if __name__ == '__main__':
    main()
