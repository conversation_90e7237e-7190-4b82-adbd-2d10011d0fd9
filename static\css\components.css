/* Verdure Components - Reusable UI Components */

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-lg);
    font-size: var(--font-size-sm);
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--gradient-primary);
    color: var(--white);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.btn-secondary {
    background: var(--light-gray);
    color: var(--charcoal);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--medium-gray);
}

.btn-danger {
    background: var(--soft-red);
    color: var(--white);
}

.btn-danger:hover:not(:disabled) {
    background: #c0392b;
}

.btn-success {
    background: var(--verdure-green);
    color: var(--white);
}

.btn-success:hover:not(:disabled) {
    background: var(--verdure-green-dark);
}

.btn-outline {
    background: transparent;
    border: 2px solid var(--verdure-green);
    color: var(--verdure-green);
}

.btn-outline:hover:not(:disabled) {
    background: var(--verdure-green);
    color: var(--white);
}

/* Button sizes */
.btn-sm {
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-xs);
}

.btn-lg {
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--font-size-lg);
}

/* AI Toggle Button */
.ai-toggle-btn {
    background: var(--charcoal-light);
    color: var(--white);
    border: none;
    border-radius: var(--radius-full);
    padding: var(--spacing-sm);
    cursor: pointer;
    transition: var(--transition-smooth);
    position: relative;
}

.ai-toggle-btn:hover {
    background: var(--charcoal);
    transform: scale(1.1);
}

.ai-toggle-btn.active {
    background: var(--gradient-primary);
    animation: pulse-glow 2s infinite;
}

.ai-toggle-btn.active::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 6px;
    height: 6px;
    background: var(--verdure-green);
    border-radius: 50%;
    animation: pulse-dot 1s infinite;
}

/* Forms */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-group label {
    display: block;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
    color: var(--charcoal);
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: var(--spacing-md);
    border: 2px solid var(--light-gray);
    border-radius: var(--radius-lg);
    font-size: var(--font-size-base);
    font-family: var(--font-family);
    color: var(--charcoal);
    background: var(--white);
    transition: var(--transition-smooth);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--verdure-green);
    box-shadow: 0 0 0 3px rgba(46, 204, 113, 0.1);
}

.form-group input[type="color"] {
    width: 60px;
    height: 40px;
    padding: 0;
    border: none;
    border-radius: var(--radius-md);
    cursor: pointer;
}

/* Search Components */
.search-input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.search-input-container input {
    flex: 1;
    padding-left: 40px;
    padding-right: 40px;
}

.search-icon {
    position: absolute;
    left: var(--spacing-md);
    color: var(--dark-gray);
    pointer-events: none;
}

.search-btn {
    position: absolute;
    right: var(--spacing-sm);
    background: none;
    border: none;
    color: var(--dark-gray);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.search-btn:hover {
    color: var(--verdure-green);
    background: var(--light-gray);
}

/* Global Search Overlay */
.global-search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(4px);
    z-index: var(--z-overlay);
    display: none;
    align-items: flex-start;
    justify-content: center;
    padding: var(--spacing-xxl);
}

.global-search-overlay.active {
    display: flex;
    animation: fade-in 0.3s ease-out;
}

.search-container {
    width: 100%;
    max-width: 800px;
    margin-top: 10vh;
}

.global-search-input {
    width: 100%;
    padding: var(--spacing-lg) var(--spacing-xl);
    font-size: var(--font-size-xl);
    border: none;
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-heavy);
    background: var(--white);
    color: var(--charcoal);
}

.global-search-input:focus {
    outline: none;
    box-shadow: var(--shadow-glow);
}

.search-results {
    margin-top: var(--spacing-lg);
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-heavy);
    max-height: 60vh;
    overflow-y: auto;
}

.search-result-item {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--light-gray);
    cursor: pointer;
    transition: var(--transition-fast);
}

.search-result-item:hover {
    background: var(--light-gray);
}

.search-result-item:last-child {
    border-bottom: none;
}

.search-result-title {
    font-weight: 600;
    margin-bottom: var(--spacing-sm);
    color: var(--charcoal);
}

.search-result-content {
    color: var(--dark-gray);
    font-size: var(--font-size-sm);
    line-height: 1.5;
    margin-bottom: var(--spacing-sm);
}

.search-result-meta {
    display: flex;
    gap: var(--spacing-md);
    font-size: var(--font-size-xs);
    color: var(--dark-gray);
}

.search-result-type {
    background: var(--light-gray);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(4px);
    z-index: var(--z-modal);
    display: none;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
}

.modal.active {
    display: flex;
    animation: fade-in 0.3s ease-out;
}

.modal-content {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-heavy);
    width: 100%;
    max-width: 500px;
    max-height: 80vh;
    overflow: hidden;
    animation: modal-slide-up 0.4s ease-out;
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--light-gray);
    background: var(--gradient-soft);
}

.modal-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--charcoal);
}

.close-btn {
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--dark-gray);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.close-btn:hover {
    background: var(--light-gray);
    color: var(--charcoal);
}

.modal-body {
    padding: var(--spacing-xl);
    max-height: 50vh;
    overflow-y: auto;
}

.modal-footer {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;
    padding: var(--spacing-xl);
    border-top: 1px solid var(--light-gray);
    background: var(--gradient-soft);
}

/* Processing Overlay */
.processing-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(4px);
    z-index: var(--z-overlay);
    display: none;
    align-items: center;
    justify-content: center;
}

.processing-overlay.active {
    display: flex;
    animation: fade-in 0.3s ease-out;
}

.processing-content {
    text-align: center;
    padding: var(--spacing-xxl);
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-heavy);
    max-width: 400px;
}

.processing-title {
    margin: var(--spacing-lg) 0 var(--spacing-md);
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--charcoal);
}

.processing-message {
    color: var(--dark-gray);
    margin-bottom: var(--spacing-lg);
}

/* Notifications */
.notification-container {
    position: fixed;
    top: var(--spacing-xl);
    right: var(--spacing-xl);
    z-index: var(--z-tooltip);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.notification {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-heavy);
    padding: var(--spacing-lg);
    min-width: 300px;
    max-width: 400px;
    border-left: 4px solid var(--verdure-green);
    animation: notification-slide-in 0.4s ease-out;
}

.notification.success {
    border-left-color: var(--verdure-green);
}

.notification.error {
    border-left-color: var(--soft-red);
}

.notification.warning {
    border-left-color: var(--warm-orange);
}

.notification.info {
    border-left-color: var(--deep-blue);
}

.notification-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-sm);
}

.notification-title {
    font-weight: 600;
    color: var(--charcoal);
}

.notification-close {
    background: none;
    border: none;
    color: var(--dark-gray);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.notification-close:hover {
    background: var(--light-gray);
}

.notification-message {
    color: var(--dark-gray);
    font-size: var(--font-size-sm);
    line-height: 1.5;
}

/* Tags */
.tag {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--light-gray);
    color: var(--charcoal);
    border-radius: var(--radius-full);
    font-size: var(--font-size-xs);
    font-weight: 500;
}

.tag.primary {
    background: var(--verdure-green);
    color: var(--white);
}

.tag.secondary {
    background: var(--deep-blue);
    color: var(--white);
}

.tag.warning {
    background: var(--warm-orange);
    color: var(--white);
}

.tag.danger {
    background: var(--soft-red);
    color: var(--white);
}

/* Calendar Components */
.mini-calendar {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
}

.calendar-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--spacing-md);
}

.calendar-nav-btn {
    background: none;
    border: none;
    color: var(--dark-gray);
    cursor: pointer;
    padding: var(--spacing-xs);
    border-radius: var(--radius-sm);
    transition: var(--transition-fast);
}

.calendar-nav-btn:hover {
    background: var(--light-gray);
    color: var(--charcoal);
}

.calendar-grid {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 2px;
}

.calendar-day {
    aspect-ratio: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: var(--transition-fast);
}

.calendar-day:hover {
    background: var(--light-gray);
}

.calendar-day.today {
    background: var(--verdure-green);
    color: var(--white);
}

.calendar-day.has-events {
    background: var(--deep-blue);
    color: var(--white);
}

.calendar-day.other-month {
    color: var(--medium-gray);
}

/* Event Components */
.event-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-sm);
    transition: var(--transition-fast);
    border-left: 4px solid transparent;
}

.event-item:hover {
    background: var(--light-gray);
}

.event-item.deadline {
    border-left-color: var(--soft-red);
}

.event-item.exam {
    border-left-color: var(--warm-orange);
}

.event-item.reminder {
    border-left-color: var(--deep-blue);
}

.event-item.study_session {
    border-left-color: var(--verdure-green);
}

.event-time {
    font-size: var(--font-size-xs);
    color: var(--dark-gray);
    font-weight: 500;
    min-width: 80px;
}

.event-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.event-title {
    font-weight: 500;
    color: var(--charcoal);
}

.event-type {
    font-size: var(--font-size-xs);
    color: var(--dark-gray);
    text-transform: capitalize;
}

.importance-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    flex-shrink: 0;
}

.importance-1 { background: var(--light-gray); }
.importance-2 { background: var(--deep-blue); }
.importance-3 { background: var(--warm-orange); }
.importance-4 { background: var(--soft-red); }
.importance-5 { 
    background: var(--soft-red);
    animation: pulse-dot 1s infinite;
}

/* AI Metrics */
.ai-metrics {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-lg);
}

.metric {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.metric-label {
    font-size: var(--font-size-sm);
    color: var(--dark-gray);
}

.metric-value {
    font-weight: 600;
    font-size: var(--font-size-lg);
    color: var(--charcoal);
}

.models-status {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.model-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-xs);
}

.model-indicator i {
    font-size: 6px;
}

.model-indicator.online i {
    color: var(--verdure-green);
}

.model-indicator.offline i {
    color: var(--soft-red);
}

.model-indicator.loading i {
    color: var(--warm-orange);
    animation: pulse-dot 1s infinite;
}
