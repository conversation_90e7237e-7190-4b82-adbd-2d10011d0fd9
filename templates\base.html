<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Verdure - AI Student Assistant{% endblock %}</title>
    
    <!-- CSS Files -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/main.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/animations.css') }}">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/components.css') }}">
    
    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css">
    
    <!-- Custom Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    {% block head %}{% endblock %}
</head>
<body>
    <div class="app-container">
        <!-- Navigation Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <div class="logo-container pulse-glow">
                    <i class="fas fa-seedling logo-icon"></i>
                    <h1 class="logo-text">Verdure</h1>
                </div>
            </div>
            
            <div class="nav-menu">
                <a href="{{ url_for('dashboard') }}" class="nav-item {% if request.endpoint == 'dashboard' %}active{% endif %}">
                    <i class="fas fa-home"></i>
                    <span>Dashboard</span>
                </a>
                
                <a href="{{ url_for('courses') }}" class="nav-item {% if request.endpoint == 'courses' %}active{% endif %}">
                    <i class="fas fa-book"></i>
                    <span>Courses</span>
                </a>
                
                <a href="#" class="nav-item disabled" title="Coming Soon">
                    <i class="fas fa-search"></i>
                    <span>Research</span>
                    <small class="coming-soon">Soon</small>
                </a>
                
                <div class="nav-divider"></div>
                
                <a href="#" class="nav-item" onclick="toggleSettings()">
                    <i class="fas fa-cog"></i>
                    <span>Settings</span>
                </a>
            </div>
            
            <!-- AI Status Indicator -->
            <div class="ai-status-container">
                <div class="ai-status" id="aiStatus">
                    <div class="status-indicator"></div>
                    <span class="status-text">AI Ready</span>
                </div>
            </div>
        </nav>
        
        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Top Bar -->
            <header class="top-bar">
                <button class="sidebar-toggle" onclick="toggleSidebar()">
                    <i class="fas fa-bars"></i>
                </button>
                
                <div class="page-title">
                    <h2>{% block page_title %}Dashboard{% endblock %}</h2>
                </div>
                
                <div class="top-bar-actions">
                    <!-- Search Toggle -->
                    <button class="action-btn" onclick="toggleGlobalSearch()" title="Global Search">
                        <i class="fas fa-search"></i>
                    </button>
                    
                    <!-- Notifications -->
                    <button class="action-btn" onclick="toggleNotifications()" title="Notifications">
                        <i class="fas fa-bell"></i>
                        <span class="notification-badge" id="notificationBadge">3</span>
                    </button>
                </div>
            </header>
            
            <!-- Global Search Overlay -->
            <div class="global-search-overlay" id="globalSearchOverlay">
                <div class="search-container">
                    <div class="search-input-container">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" 
                               class="global-search-input" 
                               id="globalSearchInput"
                               placeholder="Search across all content..."
                               autocomplete="off">
                        <button class="ai-toggle-btn" 
                                id="aiSearchToggle" 
                                onclick="toggleAISearch()"
                                title="Toggle AI-powered search">
                            <i class="fas fa-brain"></i>
                        </button>
                    </div>
                    
                    <div class="search-results" id="searchResults">
                        <!-- Search results will be populated here -->
                    </div>
                </div>
            </div>
            
            <!-- Content Area -->
            <div class="content-area">
                {% block content %}{% endblock %}
            </div>
            
            <!-- Processing Overlay -->
            <div class="processing-overlay" id="processingOverlay">
                <div class="processing-content">
                    <div class="processing-spinner"></div>
                    <h3 class="processing-title" id="processingTitle">Processing...</h3>
                    <p class="processing-message" id="processingMessage">Please wait while AI analyzes your input</p>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Notification Container -->
    <div class="notification-container" id="notificationContainer">
        <!-- Notifications will be added here dynamically -->
    </div>
    
    <!-- JavaScript Files -->
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
    <script src="{{ url_for('static', filename='js/animations.js') }}"></script>
    <script src="{{ url_for('static', filename='js/search.js') }}"></script>
    
    {% block scripts %}{% endblock %}
    
    <script>
        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            initializeApp();
        });
    </script>
</body>
</html>
