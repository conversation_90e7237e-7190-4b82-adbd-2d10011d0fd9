@echo off
REM Verdure Run Script for Windows

echo 🌱 Starting Verdure AI Student Assistant...

REM Check if virtual environment exists
if not exist "venv" (
    echo Creating virtual environment...
    python -m venv venv
)

REM Activate virtual environment
call venv\Scripts\activate.bat

REM Install dependencies
echo Installing dependencies...
pip install -r requirements.txt

REM Check for .env file
if not exist ".env" (
    echo Creating .env file from template...
    copy .env.example .env
    echo ⚠️  Please edit .env file with your API keys!
)

REM Create necessary directories
mkdir uploads 2>nul
mkdir chroma_data 2>nul

REM Run the application
echo 🚀 Starting Verdure...
python app.py
