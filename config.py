"""
Configuration settings for Verdure application
"""

import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'verdure-dev-secret-key-2025'
    
    # Database configuration
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///verdure.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # File upload configuration
    UPLOAD_FOLDER = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'uploads')
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'ppt', 'pptx'}
    
    # AI API configuration
    OPENROUTER_API_KEY = os.environ.get('OPENROUTER_API_KEY') or ''
    OPENROUTER_BASE_URL = 'https://openrouter.ai/api/v1'
    
    # AI Model configuration
    DEEPSEEK_MODEL = 'deepseek/deepseek-r1-distill-llama-70b'
    GEMINI_MODEL = 'google/gemini-2.0-flash-exp:free'
    
    # Ollama configuration
    OLLAMA_BASE_URL = 'http://localhost:11434'
    OLLAMA_EMBEDDING_MODEL = 'nomic-embed-text'
    
    # Redis configuration (for background tasks)
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    
    # Application settings
    DEBUG = os.environ.get('FLASK_ENV') == 'development'
    
    @staticmethod
    def init_app(app):
        # Create upload folder if it doesn't exist
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
