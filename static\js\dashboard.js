/**
 * Verdure Dashboard JavaScript - Dashboard-specific functionality
 */

// Dashboard state
let dashboardState = {
    currentFiles: [],
    processingQueue: [],
    aiMetrics: {
        indexedItems: 0,
        processingQueue: 0
    }
};

/**
 * Initialize dashboard
 */
function initializeDashboard() {
    console.log('🏠 Initializing Dashboard...');
    
    // Setup smart input
    initializeSmartInput();
    
    // Setup file upload
    setupFileDropZone();
    
    // Setup quick search
    initializeQuickSearch();
    
    // Load dashboard data
    loadDashboardData();
    
    // Setup real-time updates
    setupRealTimeUpdates();
}

/**
 * Initialize smart input functionality
 */
function initializeSmartInput() {
    const smartInput = document.getElementById('smartInput');
    const submitBtn = document.getElementById('submitBtn');
    
    if (smartInput) {
        // Auto-resize textarea
        smartInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = Math.min(this.scrollHeight, 200) + 'px';
            
            // Show/hide submit button based on content
            if (submitBtn) {
                if (this.value.trim()) {
                    submitBtn.classList.add('active');
                    animateElement(submitBtn, 'fade-in-scale');
                } else {
                    submitBtn.classList.remove('active');
                }
            }
        });
        
        // Handle Enter key (Ctrl+Enter to submit)
        smartInput.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' && (e.ctrlKey || e.metaKey)) {
                e.preventDefault();
                processInput();
            }
        });
        
        // Focus animation
        smartInput.addEventListener('focus', function() {
            const wrapper = document.getElementById('smartInputWrapper');
            wrapper?.classList.add('pulse-input');
        });
        
        smartInput.addEventListener('blur', function() {
            const wrapper = document.getElementById('smartInputWrapper');
            wrapper?.classList.remove('pulse-input');
        });
    }
}

/**
 * Process user input with AI
 */
async function processInput() {
    const smartInput = document.getElementById('smartInput');
    const inputText = smartInput?.value?.trim();
    
    if (!inputText && dashboardState.currentFiles.length === 0) {
        showError('Please enter some text or upload files');
        return;
    }
    
    // Show processing state
    showProcessingOverlay('Analyzing Input...', 'AI is categorizing and processing your input');
    
    try {
        const requestData = {
            input: inputText,
            files: dashboardState.currentFiles
        };
        
        const response = await fetch('/api/process_input', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(requestData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            // Show categorization result
            showCategorizationResult(data);
            
            // Clear input
            if (smartInput) smartInput.value = '';
            clearFiles();
            
            // Refresh dashboard data
            loadDashboardData();
            
            showSuccess(`Successfully processed as ${data.category}`);
        } else {
            throw new Error(data.error || 'Processing failed');
        }
        
    } catch (error) {
        console.error('Input processing error:', error);
        showError('Failed to process input: ' + error.message);
    } finally {
        hideProcessingOverlay();
    }
}

/**
 * Show categorization result animation
 */
function showCategorizationResult(data) {
    const display = document.getElementById('categorizationDisplay');
    const categoryLabel = document.getElementById('categoryLabel');
    const confidenceScore = document.getElementById('confidenceScore');
    
    if (display && categoryLabel && confidenceScore) {
        categoryLabel.textContent = data.category;
        confidenceScore.textContent = `${data.confidence}% confident`;
        
        display.classList.add('show');
        animateElement(display, 'slide-down');
        
        // Hide after 3 seconds
        setTimeout(() => {
            display.classList.remove('show');
        }, 3000);
    }
}

/**
 * Setup file drop zone
 */
function setupFileDropZone() {
    const dropZone = document.getElementById('fileDropZone');
    const fileInput = document.getElementById('fileInput');
    const inputWrapper = document.getElementById('smartInputWrapper');
    
    // Drag and drop events
    ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
        document.addEventListener(eventName, preventDefaults, false);
    });
    
    function preventDefaults(e) {
        e.preventDefault();
        e.stopPropagation();
    }
    
    // Highlight drop zone on drag
    ['dragenter', 'dragover'].forEach(eventName => {
        document.addEventListener(eventName, () => {
            dropZone?.classList.add('active');
            inputWrapper?.classList.add('drag-over');
        });
    });
    
    ['dragleave', 'drop'].forEach(eventName => {
        document.addEventListener(eventName, () => {
            dropZone?.classList.remove('active');
            inputWrapper?.classList.remove('drag-over');
        });
    });
    
    // Handle file drop
    document.addEventListener('drop', handleFileDrop);
    
    // Handle file input change
    if (fileInput) {
        fileInput.addEventListener('change', function(e) {
            handleFiles(e.target.files);
        });
    }
}

/**
 * Handle file drop
 */
function handleFileDrop(e) {
    const files = e.dataTransfer.files;
    handleFiles(files);
}

/**
 * Handle selected files
 */
async function handleFiles(files) {
    if (!files || files.length === 0) return;
    
    for (let file of files) {
        await processFile(file);
    }
    
    updateFileDisplay();
}

/**
 * Process individual file
 */
async function processFile(file) {
    // Validate file
    const maxSize = 16 * 1024 * 1024; // 16MB
    if (file.size > maxSize) {
        showError(`File ${file.name} is too large (max 16MB)`);
        return;
    }
    
    // Show upload progress
    const fileId = 'file-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    showFileProgress(fileId, file.name);
    
    try {
        const formData = new FormData();
        formData.append('file', file);
        
        const response = await fetch('/api/upload_file', {
            method: 'POST',
            body: formData
        });
        
        const data = await response.json();
        
        if (data.success) {
            dashboardState.currentFiles.push({
                id: fileId,
                name: file.name,
                type: file.type,
                size: file.size,
                processed: data.processed_data,
                category: data.category
            });
            
            updateFileProgress(fileId, 100, 'Processed');
        } else {
            throw new Error(data.error || 'Upload failed');
        }
        
    } catch (error) {
        console.error('File processing error:', error);
        showError(`Failed to process ${file.name}: ${error.message}`);
        updateFileProgress(fileId, 0, 'Error');
    }
}

/**
 * Show file upload progress
 */
function showFileProgress(fileId, fileName) {
    // This would show a progress indicator in the UI
    console.log(`Uploading ${fileName}...`);
}

/**
 * Update file progress
 */
function updateFileProgress(fileId, progress, status) {
    // This would update the progress indicator
    console.log(`File ${fileId}: ${progress}% - ${status}`);
}

/**
 * Update file display
 */
function updateFileDisplay() {
    // This would update the UI to show current files
    console.log('Current files:', dashboardState.currentFiles);
}

/**
 * Clear files
 */
function clearFiles() {
    dashboardState.currentFiles = [];
    updateFileDisplay();
}

/**
 * Trigger file upload dialog
 */
function triggerFileUpload() {
    const fileInput = document.getElementById('fileInput');
    fileInput?.click();
}

/**
 * Toggle voice input (placeholder)
 */
function toggleVoiceInput() {
    showNotification('Voice input coming soon!', 'info');
}

/**
 * Initialize quick search
 */
function initializeQuickSearch() {
    const quickSearchInput = document.getElementById('quickSearchInput');
    
    if (quickSearchInput) {
        // Debounced search as user types
        const debouncedSearch = debounce(performQuickSearch, 300);
        quickSearchInput.addEventListener('input', debouncedSearch);
    }
}

/**
 * Handle quick search keypress
 */
function handleQuickSearchKeypress(event) {
    if (event.key === 'Enter') {
        performQuickSearch();
    }
}

/**
 * Perform quick search
 */
async function performQuickSearch() {
    const input = document.getElementById('quickSearchInput');
    const results = document.getElementById('quickSearchResults');
    const query = input?.value?.trim();
    
    if (!query) {
        if (results) results.innerHTML = '';
        return;
    }
    
    // Show loading
    if (results) {
        results.innerHTML = `
            <div class="quick-search-loading">
                <div class="processing-spinner small"></div>
                <span>Searching...</span>
            </div>
        `;
    }
    
    try {
        const searchData = {
            query: query,
            use_ai_agent: false, // Quick search doesn't use AI agent
            filters: { limit: 5 } // Limit results for quick search
        };
        
        const response = await fetch('/api/semantic_search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(searchData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            displayQuickSearchResults(data.results, results);
        } else {
            if (results) {
                results.innerHTML = '<div class="search-error">Search failed</div>';
            }
        }
        
    } catch (error) {
        console.error('Quick search error:', error);
        if (results) {
            results.innerHTML = '<div class="search-error">Search failed</div>';
        }
    }
}

/**
 * Display quick search results
 */
function displayQuickSearchResults(results, container) {
    if (!container) return;
    
    if (!results || results.length === 0) {
        container.innerHTML = '<div class="no-results">No results found</div>';
        return;
    }
    
    const resultsHTML = results.map(result => `
        <div class="quick-result-item" onclick="openSearchResult('${result.id}')">
            <div class="result-title">${escapeHtml(result.metadata?.title || 'Untitled')}</div>
            <div class="result-type">${escapeHtml(result.metadata?.content_type || 'Unknown')}</div>
        </div>
    `).join('');
    
    container.innerHTML = resultsHTML;
    animateElement(container, 'fade-in');
}

/**
 * Toggle quick search AI
 */
function toggleQuickSearchAI() {
    const toggle = document.getElementById('quickSearchAIToggle');
    // This would enable/disable AI for quick search
    showNotification('Quick search AI toggle coming soon!', 'info');
}

/**
 * Load dashboard data
 */
async function loadDashboardData() {
    try {
        // Load various dashboard components
        await Promise.all([
            loadImportantDates(),
            loadAIMetrics(),
            updateCalendar()
        ]);
        
    } catch (error) {
        console.error('Failed to load dashboard data:', error);
    }
}

/**
 * Load important dates
 */
async function loadImportantDates() {
    const container = document.getElementById('importantDatesList');
    if (!container) return;
    
    try {
        // This would fetch important dates from the API
        // For now, show placeholder
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-calendar-check"></i>
                <p>No important dates scheduled</p>
            </div>
        `;
        
    } catch (error) {
        console.error('Failed to load important dates:', error);
    }
}

/**
 * Load AI metrics
 */
async function loadAIMetrics() {
    const indexedCount = document.getElementById('indexedItemsCount');
    const queueCount = document.getElementById('processingQueueCount');
    
    try {
        // This would fetch actual metrics from the API
        // For now, show placeholder values
        if (indexedCount) {
            animateValue(indexedCount, 0, dashboardState.aiMetrics.indexedItems, 1000);
        }
        
        if (queueCount) {
            animateValue(queueCount, 0, dashboardState.aiMetrics.processingQueue, 1000);
        }
        
    } catch (error) {
        console.error('Failed to load AI metrics:', error);
    }
}

/**
 * Update calendar display
 */
function updateCalendar() {
    const calendar = document.getElementById('miniCalendar');
    if (!calendar) return;
    
    // This would update the calendar with current month and events
    console.log('Updating calendar...');
}

/**
 * Setup real-time updates
 */
function setupRealTimeUpdates() {
    // This would setup WebSocket or polling for real-time updates
    setInterval(() => {
        // Update AI metrics periodically
        dashboardState.aiMetrics.indexedItems += Math.floor(Math.random() * 3);
        loadAIMetrics();
    }, 30000); // Update every 30 seconds
}

/**
 * Show create course modal
 */
function showCreateCourseModal() {
    showModal('createCourseModal');
}

/**
 * Create new course
 */
async function createCourse() {
    const form = document.getElementById('createCourseForm');
    const formData = new FormData(form);
    
    const courseData = {
        name: formData.get('name'),
        code: formData.get('code'),
        color: formData.get('color'),
        description: formData.get('description')
    };
    
    if (!courseData.name?.trim()) {
        showError('Course name is required');
        return;
    }
    
    try {
        const response = await fetch('/api/create_course', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(courseData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            closeModal('createCourseModal');
            showSuccess('Course created successfully!');
            
            // Refresh page to show new course
            setTimeout(() => {
                window.location.reload();
            }, 1000);
        } else {
            throw new Error(data.error || 'Failed to create course');
        }
        
    } catch (error) {
        console.error('Course creation error:', error);
        showError('Failed to create course: ' + error.message);
    }
}

/**
 * Open course
 */
function openCourse(courseId) {
    window.location.href = `/courses?id=${courseId}`;
}

/**
 * Expand calendar
 */
function expandCalendar() {
    showNotification('Full calendar view coming soon!', 'info');
}

/**
 * Animate number value
 */
function animateValue(element, start, end, duration) {
    if (!element) return;
    
    const range = end - start;
    const current = start;
    const increment = end > start ? 1 : -1;
    const stepTime = Math.abs(Math.floor(duration / range));
    
    const timer = setInterval(() => {
        current += increment;
        element.textContent = current;
        
        if (current === end) {
            clearInterval(timer);
        }
    }, stepTime);
}

/**
 * Check AI status periodically
 */
function checkAIStatus() {
    // This would check the status of AI models
    checkAIModelsStatus();
    
    // Set up periodic checks
    setInterval(checkAIModelsStatus, 60000); // Check every minute
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.pathname === '/' || window.location.pathname === '/dashboard') {
        setTimeout(initializeDashboard, 100);
    }
});
