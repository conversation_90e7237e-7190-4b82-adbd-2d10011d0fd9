# 🎓 AI-Powered Student Aider - Comprehensive Project Plan

## 📋 Project Overview

**Project Name:** Verdure - AI Student Assistant  
**Goal:** Create an intelligent, animated, and seamless student productivity platform powered by AI  
**Technology Stack:** Flask + HTML/CSS/JS with advanced animations  
**AI Integration:** OpenRouter API (DeepSeek R1, Gemini 2.0 Flash) + Local Ollama embeddings  

## 🏗️ Architecture Overview

```
┌─────────────────────────────────────────┐
│             Frontend Layer              │
│  ┌─────────────────────────────────────┐ │
│  │     React/Vanilla JS + CSS          │ │
│  │  ┌─────────────────────────────────┐ │ │
│  │  │    Animation Engine             │ │ │
│  │  │  - Loading bars                 │ │ │
│  │  │  - Processing indicators        │ │ │
│  │  │  - Smooth transitions           │ │ │
│  │  └─────────────────────────────────┘ │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│             Backend Layer               │
│  ┌─────────────────────────────────────┐ │
│  │           Flask API                 │ │
│  │  ┌─────────────────────────────────┐ │ │
│  │  │     AI Processing Engine        │ │ │
│  │  │  - Input Categorization         │ │ │
│  │  │  - Content Processing           │ │ │
│  │  │  - Search Intelligence          │ │ │
│  │  └─────────────────────────────────┘ │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│            Data Layer                   │
│  ┌─────────────────────────────────────┐ │
│  │        SQLite Database              │ │
│  │  ┌─────────────────────────────────┐ │ │
│  │  │      Local Ollama               │ │ │
│  │  │   (Nomic Embed Model)           │ │ │
│  │  │  - Vector embeddings            │ │ │
│  │  │  - Semantic search              │ │ │
│  │  └─────────────────────────────────┘ │ │
│  └─────────────────────────────────────┘ │
└─────────────────────────────────────────┘
```

## 🎯 Feature Breakdown & Implementation Strategy

### 1. 🏠 Dashboard (Priority: HIGH)

#### Core Components:
- **Smart Input Bar**
  - File upload capability (drag & drop)
  - Real-time AI categorization
  - Visual feedback during processing
  - Auto-suggestions based on input type

- **AI Categorization System**
  ```
  Input Types to Detect:
  ├── Tasks ("Complete assignment X", "Study for exam")
  ├── Reminders ("Remember to submit", "Check deadline")
  ├── Events ("Exam on Friday", "Meeting at 3pm")
  ├── Lecture Slides (PDF/PPT uploads)
  ├── Course Study Guides (structured documents)
  └── Content Questions ("What is photosynthesis?")
  ```

- **Integrated Calendar**
  - Event synchronization
  - Deadline tracking
  - Visual importance indicators
  - Quick-add functionality

- **Semantic Search Engine**
  - Ollama integration for embeddings
  - Advanced filtering options
  - AI-powered search agent (toggle-able)
  - Intelligent result visualization

#### Animation Requirements:
- Pulsing input bar when active
- Smooth categorization indicators
- Loading spinners for AI processing
- Floating action buttons
- Morphing icons based on content type

### 2. 📚 Courses Page (Priority: HIGH)

#### Features:
- **Course Management**
  - Create/edit course cards
  - Color-coded organization
  - Progress tracking
  - Quick navigation

- **Content Organization**
  - Lecture slides management
  - Note-taking interface
  - Study guide generation
  - AI-powered summaries

- **AI Integration**
  - Automatic content categorization
  - Summary generation (DeepSeek R1)
  - Key concept extraction
  - Study recommendation engine

#### Animation Requirements:
- Card flip animations
- Content sliding transitions
- Progress bar animations
- Hover effects with depth
- AI processing indicators

### 3. 🔬 Research Page (Priority: MEDIUM - Future)

#### Planned Features:
- Google Scholar integration
- AI research agent
- Citation management
- Relevance scoring
- Export functionality

## 🛠️ Technical Implementation Plan

### Phase 1: Foundation Setup (Week 1-2)

#### Backend Infrastructure:
```python
# Flask application structure
verdure/
├── app.py                 # Main Flask application
├── config.py              # Configuration management
├── requirements.txt       # Dependencies
├── models/
│   ├── __init__.py
│   ├── database.py        # SQLite models
│   ├── ai_processor.py    # AI integration
│   └── embeddings.py      # Ollama integration
├── routes/
│   ├── __init__.py
│   ├── dashboard.py       # Dashboard endpoints
│   ├── courses.py         # Course management
│   ├── search.py          # Search functionality
│   └── api.py             # API endpoints
├── services/
│   ├── __init__.py
│   ├── openrouter_client.py  # OpenRouter integration
│   ├── categorizer.py     # Input categorization
│   ├── file_processor.py  # File handling
│   └── calendar_service.py   # Calendar operations
├── static/
│   ├── css/
│   │   ├── main.css       # Main stylesheet
│   │   ├── animations.css # Animation definitions
│   │   └── components.css # Component styles
│   ├── js/
│   │   ├── main.js        # Main JavaScript
│   │   ├── animations.js  # Animation controllers
│   │   ├── dashboard.js   # Dashboard logic
│   │   └── search.js      # Search functionality
│   └── assets/
│       ├── icons/         # SVG icons
│       └── animations/    # Lottie files
└── templates/
    ├── base.html          # Base template
    ├── dashboard.html     # Dashboard page
    ├── courses.html       # Courses page
    └── components/        # Reusable components
```

#### Key Dependencies:
```txt
Flask==2.3.3
SQLAlchemy==2.0.23
requests==2.31.0
python-dotenv==1.0.0
Pillow==10.1.0
PyPDF2==3.0.1
python-pptx==0.6.23
sentence-transformers==2.2.2
chromadb==0.4.18
celery==5.3.4        # For background processing
redis==5.0.1         # Celery broker
```

### Phase 2: Core AI Integration (Week 3-4)

#### AI Service Architecture:
```python
class AIProcessor:
    def __init__(self):
        self.openrouter_client = OpenRouterClient()
        self.ollama_client = OllamaClient()
        
    async def categorize_input(self, text: str, file_data: dict = None):
        """Categorize user input using DeepSeek R1"""
        
    async def process_content(self, content: str, content_type: str):
        """Process content based on category"""
        
    async def generate_embeddings(self, text: str):
        """Generate embeddings using Ollama nomic-embed"""
        
    async def semantic_search(self, query: str, filters: dict = None):
        """Perform semantic search with optional AI agent"""
```

#### Input Categorization Logic:
```python
CATEGORY_PROMPTS = {
    "system": """You are an intelligent categorization system for a student assistant app. 
    Analyze the user input and categorize it into one of these types:
    - TASK: Action items, assignments, things to do
    - REMINDER: Time-based notifications, deadlines
    - EVENT: Scheduled activities, meetings, exams
    - LECTURE_SLIDES: Uploaded presentation files
    - STUDY_GUIDE: Study materials, notes, guides
    - QUESTION: Questions about previously indexed content
    
    Return only the category name and confidence score (0-100)."""
}
```

### Phase 3: Database Design (Week 2-3)

#### Database Schema:
```sql
-- Users table (for future auth implementation)
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Courses table
CREATE TABLE courses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20),
    color VARCHAR(7) DEFAULT '#3498db',
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Content table (unified content storage)
CREATE TABLE content (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    course_id INTEGER,
    title VARCHAR(200),
    content_type VARCHAR(50), -- task, reminder, event, slide, guide, note
    content TEXT,
    file_path VARCHAR(500),
    metadata JSON, -- Store additional data as JSON
    embedding_id VARCHAR(100), -- Reference to vector database
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (course_id) REFERENCES courses (id)
);

-- Calendar events
CREATE TABLE calendar_events (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    content_id INTEGER,
    title VARCHAR(200),
    description TEXT,
    start_datetime TIMESTAMP,
    end_datetime TIMESTAMP,
    event_type VARCHAR(50), -- reminder, deadline, exam, meeting
    importance INTEGER DEFAULT 1, -- 1-5 scale
    FOREIGN KEY (content_id) REFERENCES content (id)
);

-- Search history
CREATE TABLE search_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    query TEXT,
    results_count INTEGER,
    filters JSON,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Phase 4: Frontend Animation System (Week 4-5)

#### Animation Framework:
```css
/* Core animation system */
:root {
    --primary-color: #3498db;
    --secondary-color: #2ecc71;
    --accent-color: #e74c3c;
    --bg-primary: #f8f9fa;
    --text-primary: #2c3e50;
    --shadow-light: 0 2px 10px rgba(0,0,0,0.1);
    --shadow-heavy: 0 10px 30px rgba(0,0,0,0.2);
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

/* Alive interface animations */
.pulse-input {
    animation: pulse-glow 2s infinite;
}

@keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 5px var(--primary-color); }
    50% { box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color); }
}

.processing-indicator {
    animation: processing-dots 1.5s infinite;
}

@keyframes processing-dots {
    0% { content: "Processing"; }
    33% { content: "Processing."; }
    66% { content: "Processing.."; }
    100% { content: "Processing..."; }
}

/* Card animations */
.course-card {
    transition: var(--transition-smooth);
    transform-style: preserve-3d;
}

.course-card:hover {
    transform: translateY(-10px) rotateX(5deg);
    box-shadow: var(--shadow-heavy);
}

/* Loading animations */
.loading-bar {
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    animation: loading-slide 2s infinite;
}

@keyframes loading-slide {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}
```

#### JavaScript Animation Controllers:
```javascript
class AnimationController {
    constructor() {
        this.animations = new Map();
    }
    
    showProcessing(element, message = "Processing") {
        // Animated processing indicator
    }
    
    hideProcessing(element) {
        // Clean removal of processing state
    }
    
    categorizeAnimation(category) {
        // Visual feedback for categorization
    }
    
    searchResultsAnimation(results) {
        // Staggered result appearance
    }
    
    fileUploadAnimation(file) {
        // Upload progress with visual feedback
    }
}
```

### Phase 5: Advanced Features (Week 6-8)

#### Semantic Search Implementation:
```python
class SemanticSearchEngine:
    def __init__(self):
        self.chroma_client = chromadb.Client()
        self.collection = self.chroma_client.create_collection("student_content")
        
    async def index_content(self, content_id: str, text: str, metadata: dict):
        """Index content with embeddings"""
        embedding = await self.generate_embedding(text)
        self.collection.add(
            embeddings=[embedding],
            documents=[text],
            metadatas=[metadata],
            ids=[content_id]
        )
    
    async def search(self, query: str, filters: dict = None, n_results: int = 10):
        """Perform semantic search"""
        query_embedding = await self.generate_embedding(query)
        results = self.collection.query(
            query_embeddings=[query_embedding],
            n_results=n_results,
            where=filters
        )
        return results
        
    async def ai_search_agent(self, query: str):
        """AI agent that performs intelligent search"""
        # Use DeepSeek R1 to understand search intent
        # Perform multiple searches with different strategies
        # Intelligently combine and rank results
        # Return structured, contextualized results
```

#### Background Processing System:
```python
from celery import Celery

celery_app = Celery('verdure')

@celery_app.task
def process_uploaded_file(file_path: str, content_id: str):
    """Background processing of uploaded files"""
    # Extract text from PDFs, images, etc.
    # Generate embeddings
    # Update database
    # Send progress updates via WebSocket
    
@celery_app.task
def generate_study_guide(course_id: str, content_ids: list):
    """Generate comprehensive study guides"""
    # Collect all course content
    # Use AI to create structured study guide
    # Save and notify user
```

## 🎨 UI/UX Design Philosophy

### Design Principles:
1. **Alive Interface**: Every element should feel responsive and alive
2. **Seamless Processing**: Background operations with clear visual feedback
3. **Intelligent Categorization**: Automatic sorting without user intervention
4. **Beautiful Animations**: Purposeful animations that enhance UX
5. **Intuitive Navigation**: Natural flow between features

### Color Scheme:
```css
/* Primary Palette */
--verdure-green: #2ecc71;     /* Primary brand color */
--deep-blue: #3498db;         /* Secondary accent */
--warm-orange: #f39c12;       /* Alert/warning */
--soft-red: #e74c3c;          /* Error/urgent */
--charcoal: #2c3e50;          /* Text primary */
--light-gray: #ecf0f1;        /* Background */
--white: #ffffff;             /* Cards/content */

/* Gradient Combinations */
--gradient-primary: linear-gradient(135deg, #2ecc71, #3498db);
--gradient-warm: linear-gradient(135deg, #f39c12, #e74c3c);
--gradient-cool: linear-gradient(135deg, #3498db, #9b59b6);
```

### Animation Library:
- **Micro-interactions**: Button hovers, input focus
- **Loading states**: Progress bars, spinners, skeleton screens
- **Transitions**: Page changes, modal appearances
- **Feedback**: Success/error animations, processing indicators
- **Ambient**: Subtle background animations, floating elements

## 📅 Development Timeline

### Week 1-2: Foundation
- [ ] Flask application setup
- [ ] Basic database design
- [ ] Core routing structure
- [ ] OpenRouter integration
- [ ] Basic UI framework

### Week 3-4: Core Features
- [ ] Input categorization system
- [ ] File upload processing
- [ ] Basic dashboard functionality
- [ ] Calendar integration
- [ ] Course management system

### Week 5-6: AI Integration
- [ ] Ollama setup and embeddings
- [ ] Semantic search implementation
- [ ] Advanced AI categorization
- [ ] Content processing pipeline
- [ ] Background task system

### Week 7-8: Polish & Advanced Features
- [ ] Animation system implementation
- [ ] Advanced search features
- [ ] Study guide generation
- [ ] Performance optimization
- [ ] Testing and debugging

### Week 9-10: Future Features
- [ ] Research page placeholder
- [ ] Authentication system prep
- [ ] Advanced analytics
- [ ] Mobile responsiveness
- [ ] Deployment preparation

## 🔧 Technical Considerations

### Performance Optimization:
1. **Lazy Loading**: Load content as needed
2. **Caching**: Redis for frequent operations
3. **Database Indexing**: Optimize queries
4. **CDN**: Static asset delivery
5. **Compression**: Gzip for responses

### Security Considerations:
1. **File Upload Validation**: Secure file handling
2. **Input Sanitization**: Prevent injection attacks
3. **Rate Limiting**: API protection
4. **CORS Configuration**: Secure cross-origin requests
5. **Environment Variables**: Secure API keys

### Scalability Planning:
1. **Modular Architecture**: Easy feature addition
2. **API Design**: RESTful and extensible
3. **Database Design**: Normalized and efficient
4. **Background Jobs**: Scalable processing
5. **Monitoring**: Performance tracking

## 🚀 Deployment Strategy

### Development Environment:
```bash
# Local development setup
pip install -r requirements.txt
redis-server
ollama serve
python app.py
```

### Production Considerations:
- **Docker containerization**
- **Nginx reverse proxy**
- **Redis cluster for caching**
- **Backup strategy for data**
- **Monitoring and logging**

## 🎯 Success Metrics

### User Experience:
- [ ] Input categorization accuracy > 90%
- [ ] Search results relevance score > 85%
- [ ] Page load times < 2 seconds
- [ ] Animation smoothness (60fps)
- [ ] Zero-downtime background processing

### Technical Metrics:
- [ ] API response times < 200ms
- [ ] Database query optimization
- [ ] Memory usage optimization
- [ ] Error rate < 1%
- [ ] Test coverage > 80%

## 🔮 Future Enhancements

### Short-term (Next 3 months):
- User authentication system
- Mobile app development
- Advanced analytics dashboard
- Integration with external calendar services
- Collaborative features

### Long-term (6+ months):
- Research page with Google Scholar
- AI tutoring system
- Study group features
- Advanced reporting
- Machine learning personalization

---

## 📝 Notes & Considerations

### AI Model Selection Rationale:
- **DeepSeek R1**: Excellent reasoning for categorization and content analysis
- **Gemini 2.0 Flash**: Vision capabilities for processing images/documents
- **Nomic Embed**: Local embeddings for privacy and speed

### Technology Stack Justification:
- **Flask**: Lightweight, flexible, easy to extend
- **SQLite**: Simple setup, suitable for single-user initially
- **Vanilla JS**: Full control over animations, lighter weight
- **Ollama**: Local AI for privacy and reduced API costs

This comprehensive plan provides a roadmap for creating an intelligent, animated, and highly functional student assistant application that feels truly alive and responsive to user needs.
