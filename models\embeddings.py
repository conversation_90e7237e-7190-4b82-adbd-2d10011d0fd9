"""
Embedding service for semantic search using Ollama
"""

import asyncio
import aiohttp
import json
import time
from typing import List, Dict, Any, Optional
import chromadb
from chromadb.config import Settings
from config import Config
from models.database import SearchHistory, db

class EmbeddingService:
    """Service for generating embeddings and semantic search"""
    
    def __init__(self):
        self.ollama_url = Config.OLLAMA_BASE_URL
        self.embedding_model = Config.OLLAMA_EMBEDDING_MODEL
        
        # Initialize ChromaDB
        self.chroma_client = chromadb.Client(Settings(
            persist_directory="./chroma_data",
            anonymized_telemetry=False
        ))
        
        # Get or create collection
        try:
            self.collection = self.chroma_client.get_collection("verdure_content")
        except:
            self.collection = self.chroma_client.create_collection(
                name="verdure_content",
                metadata={"description": "Verdure student content embeddings"}
            )
    
    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for text using Ollama"""
        
        try:
            async with aiohttp.ClientSession() as session:
                payload = {
                    "model": self.embedding_model,
                    "prompt": text
                }
                
                async with session.post(
                    f"{self.ollama_url}/api/embeddings",
                    json=payload
                ) as response:
                    if response.status == 200:
                        result = await response.json()
                        return result.get('embedding', [])
                    else:
                        error_text = await response.text()
                        print(f"Ollama embedding error: {error_text}")
                        return []
                        
        except Exception as e:
            print(f"Error generating embedding: {str(e)}")
            return []
    
    async def index_content(self, content_id: str, text: str, metadata: Dict[str, Any]):
        """Index content with embeddings for semantic search"""
        
        try:
            # Generate embedding
            embedding = await self.generate_embedding(text)
            
            if not embedding:
                print(f"Failed to generate embedding for content {content_id}")
                return False
            
            # Add to ChromaDB
            self.collection.add(
                embeddings=[embedding],
                documents=[text],
                metadatas=[metadata],
                ids=[content_id]
            )
            
            return True
            
        except Exception as e:
            print(f"Error indexing content {content_id}: {str(e)}")
            return False
    
    async def search(self, query: str, filters: Optional[Dict] = None, 
                    n_results: int = 10) -> Dict[str, Any]:
        """Perform semantic search"""
        
        start_time = time.time()
        
        try:
            # Generate query embedding
            query_embedding = await self.generate_embedding(query)
            
            if not query_embedding:
                return {
                    'results': [],
                    'query': query,
                    'error': 'Failed to generate query embedding'
                }
            
            # Search in ChromaDB
            search_results = self.collection.query(
                query_embeddings=[query_embedding],
                n_results=n_results,
                where=filters or {}
            )
            
            # Format results
            results = []
            if search_results['ids'] and search_results['ids'][0]:
                for i, content_id in enumerate(search_results['ids'][0]):
                    result = {
                        'id': content_id,
                        'content': search_results['documents'][0][i],
                        'metadata': search_results['metadatas'][0][i],
                        'distance': search_results['distances'][0][i] if search_results['distances'] else None
                    }
                    results.append(result)
            
            execution_time = time.time() - start_time
            
            # Save search history
            search_history = SearchHistory(
                query=query,
                results_count=len(results),
                execution_time=execution_time
            )
            search_history.set_filters(filters or {})
            db.session.add(search_history)
            db.session.commit()
            
            return {
                'results': results,
                'query': query,
                'execution_time': execution_time,
                'total_results': len(results)
            }
            
        except Exception as e:
            return {
                'results': [],
                'query': query,
                'error': str(e)
            }
    
    async def ai_search_agent(self, query: str, filters: Optional[Dict] = None) -> Dict[str, Any]:
        """AI-powered intelligent search with query understanding"""
        
        try:
            # First, get available content for context
            all_content = self.collection.get()
            content_summary = {
                'total_items': len(all_content['ids']) if all_content['ids'] else 0,
                'content_types': set()
            }
            
            if all_content['metadatas']:
                for metadata in all_content['metadatas']:
                    if 'content_type' in metadata:
                        content_summary['content_types'].add(metadata['content_type'])
            
            # Use AI to analyze query and determine search strategy
            from services.openrouter_client import OpenRouterClient
            ai_client = OpenRouterClient()
            
            search_analysis = await ai_client.intelligent_search(
                query, 
                list(content_summary['content_types'])
            )
            
            # Perform multiple search strategies based on AI analysis
            search_results = []
            
            # Primary semantic search
            primary_results = await self.search(query, filters)
            search_results.extend(primary_results['results'])
            
            # Try related queries if suggested by AI
            if 'related_queries' in search_analysis:
                for related_query in search_analysis['related_queries'][:2]:  # Limit to 2
                    related_results = await self.search(related_query, filters, n_results=3)
                    search_results.extend(related_results['results'])
            
            # Remove duplicates and rank results
            unique_results = {}
            for result in search_results:
                if result['id'] not in unique_results:
                    unique_results[result['id']] = result
            
            final_results = list(unique_results.values())[:10]  # Limit to top 10
            
            return {
                'results': final_results,
                'query': query,
                'search_strategy': search_analysis.get('strategy', 'semantic'),
                'ai_analysis': search_analysis,
                'total_results': len(final_results)
            }
            
        except Exception as e:
            # Fallback to regular search
            return await self.search(query, filters)
    
    def update_content(self, content_id: str, text: str, metadata: Dict[str, Any]):
        """Update existing content in the vector database"""
        
        try:
            # Delete existing
            self.collection.delete(ids=[content_id])
            
            # Re-index with new content
            asyncio.create_task(self.index_content(content_id, text, metadata))
            
        except Exception as e:
            print(f"Error updating content {content_id}: {str(e)}")
    
    def delete_content(self, content_id: str):
        """Remove content from vector database"""
        
        try:
            self.collection.delete(ids=[content_id])
        except Exception as e:
            print(f"Error deleting content {content_id}: {str(e)}")
    
    def get_similar_content(self, content_id: str, n_results: int = 5) -> List[Dict[str, Any]]:
        """Find content similar to a specific piece of content"""
        
        try:
            # Get the content
            content_result = self.collection.get(ids=[content_id])
            
            if not content_result['documents'] or not content_result['documents'][0]:
                return []
            
            # Use its text to find similar content
            content_text = content_result['documents'][0]
            
            # Perform search (exclude the original content)
            search_task = asyncio.create_task(self.search(content_text, n_results=n_results + 1))
            results = asyncio.get_event_loop().run_until_complete(search_task)
            
            # Filter out the original content
            similar_results = [r for r in results['results'] if r['id'] != content_id]
            
            return similar_results[:n_results]
            
        except Exception as e:
            print(f"Error finding similar content for {content_id}: {str(e)}")
            return []
    
    def get_search_analytics(self) -> Dict[str, Any]:
        """Get search analytics and insights"""
        
        try:
            # Get recent search history
            recent_searches = SearchHistory.query.order_by(
                SearchHistory.created_at.desc()
            ).limit(100).all()
            
            # Calculate analytics
            total_searches = len(recent_searches)
            avg_execution_time = sum(s.execution_time or 0 for s in recent_searches) / max(total_searches, 1)
            
            # Most common queries
            query_counts = {}
            for search in recent_searches:
                query_counts[search.query] = query_counts.get(search.query, 0) + 1
            
            top_queries = sorted(query_counts.items(), key=lambda x: x[1], reverse=True)[:10]
            
            return {
                'total_searches': total_searches,
                'avg_execution_time': avg_execution_time,
                'top_queries': top_queries,
                'total_indexed_items': len(self.collection.get()['ids']) if self.collection.get()['ids'] else 0
            }
            
        except Exception as e:
            return {
                'total_searches': 0,
                'avg_execution_time': 0,
                'top_queries': [],
                'total_indexed_items': 0,
                'error': str(e)
            }
