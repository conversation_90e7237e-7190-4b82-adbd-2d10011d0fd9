/**
 * Verdure Search JavaScript - Advanced search functionality
 */

// Search state
let searchState = {
    currentQuery: '',
    filters: {},
    aiEnabled: false,
    history: [],
    results: []
};

/**
 * Initialize search functionality
 */
function initializeSearch() {
    console.log('🔍 Initializing Search...');
    
    // Setup search input handlers
    setupSearchInputs();
    
    // Setup search filters
    setupSearchFilters();
    
    // Load search history
    loadSearchHistory();
}

/**
 * Setup search input handlers
 */
function setupSearchInputs() {
    const globalInput = document.getElementById('globalSearchInput');
    const quickInput = document.getElementById('quickSearchInput');
    
    if (globalInput) {
        globalInput.addEventListener('input', debounce(handleGlobalSearchInput, 300));
        globalInput.addEventListener('keydown', handleSearchKeydown);
        globalInput.addEventListener('focus', handleSearchFocus);
        globalInput.addEventListener('blur', handleSearchBlur);
    }
    
    if (quickInput) {
        quickInput.addEventListener('input', debounce(handleQuickSearchInput, 300));
        quickInput.addEventListener('keydown', handleSearchKeydown);
    }
}

/**
 * Handle global search input
 */
function handleGlobalSearchInput(event) {
    const query = event.target.value.trim();
    searchState.currentQuery = query;
    
    if (query.length > 2) {
        performGlobalSearch();
    } else {
        clearSearchResults();
    }
}

/**
 * Handle quick search input
 */
function handleQuickSearchInput(event) {
    const query = event.target.value.trim();
    
    if (query.length > 2) {
        performQuickSearch();
    } else {
        clearQuickSearchResults();
    }
}

/**
 * Handle search keydown events
 */
function handleSearchKeydown(event) {
    switch (event.key) {
        case 'Enter':
            event.preventDefault();
            if (event.target.id === 'globalSearchInput') {
                performGlobalSearch();
            } else if (event.target.id === 'quickSearchInput') {
                performQuickSearch();
            }
            break;
            
        case 'Escape':
            if (event.target.id === 'globalSearchInput') {
                toggleGlobalSearch();
            } else {
                event.target.blur();
            }
            break;
            
        case 'ArrowDown':
            event.preventDefault();
            navigateSearchResults('down');
            break;
            
        case 'ArrowUp':
            event.preventDefault();
            navigateSearchResults('up');
            break;
    }
}

/**
 * Handle search focus
 */
function handleSearchFocus(event) {
    const container = event.target.closest('.search-input-container');
    if (container) {
        container.classList.add('focused');
        animateElement(container, 'pulse-glow');
    }
}

/**
 * Handle search blur
 */
function handleSearchBlur(event) {
    const container = event.target.closest('.search-input-container');
    if (container) {
        container.classList.remove('focused');
    }
}

/**
 * Perform advanced semantic search
 */
async function performSemanticSearch(query, options = {}) {
    const searchOptions = {
        query: query,
        use_ai_agent: options.useAI || searchState.aiEnabled,
        filters: options.filters || searchState.filters,
        n_results: options.limit || 20
    };
    
    showSearchLoading(options.container);
    
    try {
        const response = await fetch('/api/semantic_search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(searchOptions)
        });
        
        const data = await response.json();
        
        if (data.success) {
            searchState.results = data.results;
            
            // Add to search history
            addToSearchHistory(query, data.results.length);
            
            // Display results
            if (options.container) {
                displaySearchResults(data.results, options.container);
            }
            
            // Show search insights if AI was used
            if (data.ai_analysis) {
                showSearchInsights(data.ai_analysis);
            }
            
            return data.results;
        } else {
            throw new Error(data.error || 'Search failed');
        }
        
    } catch (error) {
        console.error('Semantic search error:', error);
        showSearchError(error.message, options.container);
        return [];
    }
}

/**
 * Show search loading state
 */
function showSearchLoading(container) {
    if (!container) return;
    
    container.innerHTML = `
        <div class="search-loading animate-fade-in">
            <div class="processing-spinner"></div>
            <div class="loading-text">
                <h3>Searching...</h3>
                <p>${searchState.aiEnabled ? 'AI is analyzing your query' : 'Searching through your content'}</p>
            </div>
        </div>
    `;
}

/**
 * Display search results with animations
 */
function displaySearchResults(results, container) {
    if (!container) return;
    
    if (!results || results.length === 0) {
        container.innerHTML = `
            <div class="empty-search-state animate-fade-in">
                <i class="fas fa-search"></i>
                <h3>No results found</h3>
                <p>Try adjusting your search terms or filters</p>
                <button class="btn btn-outline btn-sm" onclick="showSearchTips()">
                    <i class="fas fa-lightbulb"></i>
                    Search Tips
                </button>
            </div>
        `;
        return;
    }
    
    const resultsHTML = results.map((result, index) => {
        const relevanceScore = Math.round((1 - (result.distance || 0)) * 100);
        const contentPreview = truncateText(result.content || '', 200);
        
        return `
            <div class="search-result-item stagger-item" 
                 data-result-id="${result.id}"
                 onclick="openSearchResult('${result.id}', ${index})"
                 style="animation-delay: ${index * 0.1}s">
                
                <div class="result-header">
                    <div class="result-title-section">
                        <h4 class="result-title">${escapeHtml(result.metadata?.title || 'Untitled')}</h4>
                        <div class="result-meta">
                            <span class="result-type tag ${result.metadata?.content_type}">${escapeHtml(result.metadata?.content_type || 'Unknown')}</span>
                            <span class="result-score">Relevance: ${relevanceScore}%</span>
                            ${result.metadata?.course ? `<span class="result-course">${escapeHtml(result.metadata.course)}</span>` : ''}
                        </div>
                    </div>
                    
                    <div class="result-actions">
                        <button class="action-btn" onclick="event.stopPropagation(); shareResult('${result.id}')" title="Share">
                            <i class="fas fa-share"></i>
                        </button>
                        <button class="action-btn" onclick="event.stopPropagation(); bookmarkResult('${result.id}')" title="Bookmark">
                            <i class="fas fa-bookmark"></i>
                        </button>
                    </div>
                </div>
                
                <div class="result-content">
                    <p>${escapeHtml(contentPreview)}</p>
                </div>
                
                ${result.metadata?.tags ? `
                    <div class="result-tags">
                        ${result.metadata.tags.split(',').map(tag => 
                            `<span class="tag tag-sm">${escapeHtml(tag.trim())}</span>`
                        ).join('')}
                    </div>
                ` : ''}
                
                <div class="result-footer">
                    <span class="result-date">${formatDate(result.metadata?.created_at)}</span>
                    <span class="result-size">${formatFileSize(result.content?.length || 0)}</span>
                </div>
            </div>
        `;
    }).join('');
    
    container.innerHTML = `
        <div class="search-results-header">
            <h3>Found ${results.length} result${results.length !== 1 ? 's' : ''}</h3>
            <div class="results-actions">
                <button class="btn btn-outline btn-sm" onclick="exportSearchResults()">
                    <i class="fas fa-download"></i>
                    Export
                </button>
                <button class="btn btn-outline btn-sm" onclick="refineSearch()">
                    <i class="fas fa-filter"></i>
                    Refine
                </button>
            </div>
        </div>
        <div class="stagger-container">
            ${resultsHTML}
        </div>
    `;
    
    animateElement(container, 'fade-in');
}

/**
 * Show search error
 */
function showSearchError(message, container) {
    if (!container) return;
    
    container.innerHTML = `
        <div class="search-error animate-shake">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>Search Error</h3>
            <p>${escapeHtml(message)}</p>
            <button class="btn btn-primary btn-sm" onclick="retryLastSearch()">
                <i class="fas fa-redo"></i>
                Try Again
            </button>
        </div>
    `;
}

/**
 * Show search insights from AI analysis
 */
function showSearchInsights(analysis) {
    if (!analysis || !analysis.suggestions) return;
    
    const insights = document.createElement('div');
    insights.className = 'search-insights animate-slide-down';
    insights.innerHTML = `
        <div class="insights-header">
            <i class="fas fa-brain"></i>
            <h4>AI Search Insights</h4>
        </div>
        <div class="insights-content">
            ${analysis.suggestions.map(suggestion => `
                <div class="insight-item">
                    <i class="fas fa-lightbulb"></i>
                    <span>${escapeHtml(suggestion)}</span>
                </div>
            `).join('')}
        </div>
        <div class="insights-actions">
            <button class="btn btn-outline btn-sm" onclick="dismissInsights()">Dismiss</button>
        </div>
    `;
    
    // Insert insights above results
    const resultsContainer = document.getElementById('searchResults');
    if (resultsContainer && resultsContainer.firstChild) {
        resultsContainer.insertBefore(insights, resultsContainer.firstChild);
    }
}

/**
 * Setup search filters
 */
function setupSearchFilters() {
    // This would setup filter UI
    console.log('Setting up search filters...');
}

/**
 * Apply search filters
 */
function applySearchFilters(filters) {
    searchState.filters = { ...searchState.filters, ...filters };
    
    if (searchState.currentQuery) {
        performGlobalSearch();
    }
}

/**
 * Clear search filters
 */
function clearSearchFilters() {
    searchState.filters = {};
    
    if (searchState.currentQuery) {
        performGlobalSearch();
    }
}

/**
 * Navigate search results with keyboard
 */
function navigateSearchResults(direction) {
    const results = document.querySelectorAll('.search-result-item');
    const current = document.querySelector('.search-result-item.highlighted');
    
    let newIndex = 0;
    
    if (current) {
        const currentIndex = Array.from(results).indexOf(current);
        newIndex = direction === 'down' 
            ? Math.min(currentIndex + 1, results.length - 1)
            : Math.max(currentIndex - 1, 0);
        
        current.classList.remove('highlighted');
    }
    
    if (results[newIndex]) {
        results[newIndex].classList.add('highlighted');
        results[newIndex].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
    }
}

/**
 * Open search result
 */
function openSearchResult(resultId, index) {
    const result = searchState.results[index];
    if (!result) return;
    
    // Track result click
    trackSearchInteraction('result_click', resultId);
    
    // Open result based on content type
    switch (result.metadata?.content_type) {
        case 'task':
            openTask(resultId);
            break;
        case 'course':
            openCourse(resultId);
            break;
        case 'lecture_slides':
            openLectureSlides(resultId);
            break;
        default:
            openContentModal(result);
    }
}

/**
 * Open content in modal
 */
function openContentModal(result) {
    const modal = document.createElement('div');
    modal.className = 'modal active';
    modal.id = 'contentModal';
    
    modal.innerHTML = `
        <div class="modal-content">
            <div class="modal-header">
                <h3>${escapeHtml(result.metadata?.title || 'Content')}</h3>
                <button class="close-btn" onclick="closeModal('contentModal')">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <div class="modal-body">
                <div class="content-meta">
                    <span class="tag ${result.metadata?.content_type}">${result.metadata?.content_type || 'Unknown'}</span>
                    <span class="content-date">${formatDate(result.metadata?.created_at)}</span>
                </div>
                <div class="content-text">
                    ${escapeHtml(result.content || 'No content available')}
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeModal('contentModal')">Close</button>
                <button class="btn btn-primary" onclick="editContent('${result.id}')">Edit</button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
    animateElement(modal, 'modal-enter');
}

/**
 * Search history management
 */
function addToSearchHistory(query, resultCount) {
    const historyItem = {
        query: query,
        timestamp: new Date().toISOString(),
        resultCount: resultCount
    };
    
    searchState.history.unshift(historyItem);
    
    // Keep only last 50 searches
    if (searchState.history.length > 50) {
        searchState.history = searchState.history.slice(0, 50);
    }
    
    saveSearchHistory();
}

/**
 * Load search history from localStorage
 */
function loadSearchHistory() {
    try {
        const history = localStorage.getItem('verdure_search_history');
        if (history) {
            searchState.history = JSON.parse(history);
        }
    } catch (error) {
        console.error('Failed to load search history:', error);
        searchState.history = [];
    }
}

/**
 * Save search history to localStorage
 */
function saveSearchHistory() {
    try {
        localStorage.setItem('verdure_search_history', JSON.stringify(searchState.history));
    } catch (error) {
        console.error('Failed to save search history:', error);
    }
}

/**
 * Show search history
 */
function showSearchHistory() {
    if (searchState.history.length === 0) {
        return '<div class="empty-state">No search history</div>';
    }
    
    return searchState.history.slice(0, 10).map(item => `
        <div class="history-item" onclick="repeatSearch('${escapeHtml(item.query)}')">
            <div class="history-query">${escapeHtml(item.query)}</div>
            <div class="history-meta">
                <span class="history-count">${item.resultCount} results</span>
                <span class="history-date">${formatRelativeTime(item.timestamp)}</span>
            </div>
        </div>
    `).join('');
}

/**
 * Repeat a search from history
 */
function repeatSearch(query) {
    const globalInput = document.getElementById('globalSearchInput');
    if (globalInput) {
        globalInput.value = query;
        searchState.currentQuery = query;
        performGlobalSearch();
    }
}

/**
 * Clear search results
 */
function clearSearchResults() {
    const container = document.getElementById('searchResults');
    if (container) {
        container.innerHTML = '';
    }
}

/**
 * Clear quick search results
 */
function clearQuickSearchResults() {
    const container = document.getElementById('quickSearchResults');
    if (container) {
        container.innerHTML = '';
    }
}

/**
 * Track search interactions for analytics
 */
function trackSearchInteraction(type, data) {
    // This would send analytics data
    console.log('Search interaction:', type, data);
}

/**
 * Utility functions for search
 */
function formatDate(dateString) {
    if (!dateString) return 'Unknown date';
    
    try {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    } catch (error) {
        return 'Invalid date';
    }
}

function formatRelativeTime(dateString) {
    if (!dateString) return 'Unknown time';
    
    try {
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
        
        if (diffDays === 0) return 'Today';
        if (diffDays === 1) return 'Yesterday';
        if (diffDays < 7) return `${diffDays} days ago`;
        if (diffDays < 30) return `${Math.floor(diffDays / 7)} weeks ago`;
        return date.toLocaleDateString();
    } catch (error) {
        return 'Unknown time';
    }
}

function formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
}

// Initialize search when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(initializeSearch, 100);
});
