/**
 * Verdure Main JavaScript - Core App Functionality
 */

// Global app state
window.VerdureApp = {
    initialized: false,
    processing: false,
    searchAIEnabled: false,
    notifications: [],
    currentUser: null
};

/**
 * Initialize the application
 */
function initializeApp() {
    if (window.VerdureApp.initialized) return;
    
    console.log('🌱 Initializing Verdure AI Student Assistant...');
    
    // Initialize components
    initializeEventListeners();
    initializeAnimations();
    initializeFileUpload();
    initializeGlobalSearch();
    
    // Check AI status
    checkAIModelsStatus();
    
    // Load user preferences
    loadUserPreferences();
    
    window.VerdureApp.initialized = true;
    
    // Show welcome animation
    showWelcomeAnimation();
    
    console.log('✅ Verdure initialized successfully!');
}

/**
 * Initialize event listeners
 */
function initializeEventListeners() {
    // Sidebar toggle
    const sidebarToggle = document.querySelector('.sidebar-toggle');
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', toggleSidebar);
    }
    
    // Global search toggle
    const globalSearchBtn = document.querySelector('[onclick="toggleGlobalSearch()"]');
    if (globalSearchBtn) {
        globalSearchBtn.addEventListener('click', toggleGlobalSearch);
    }
    
    // Escape key handling
    document.addEventListener('keydown', handleKeyPress);
    
    // Click outside to close modals
    document.addEventListener('click', handleOutsideClick);
    
    // Window resize handling
    window.addEventListener('resize', handleWindowResize);
}

/**
 * Handle keyboard shortcuts
 */
function handleKeyPress(event) {
    // Global search shortcut (Ctrl/Cmd + K)
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        toggleGlobalSearch();
    }
    
    // Escape key - close overlays
    if (event.key === 'Escape') {
        closeAllOverlays();
    }
    
    // Enter key in search
    if (event.key === 'Enter' && event.target.classList.contains('global-search-input')) {
        performGlobalSearch();
    }
}

/**
 * Handle clicks outside of modals/overlays
 */
function handleOutsideClick(event) {
    // Close global search if clicking outside
    const searchOverlay = document.getElementById('globalSearchOverlay');
    if (searchOverlay && searchOverlay.classList.contains('active')) {
        if (event.target === searchOverlay) {
            toggleGlobalSearch();
        }
    }
    
    // Close modals if clicking outside
    const activeModal = document.querySelector('.modal.active');
    if (activeModal && event.target === activeModal) {
        closeModal(activeModal.id);
    }
}

/**
 * Handle window resize
 */
function handleWindowResize() {
    // Update sidebar state for responsive design
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.querySelector('.main-content');
    
    if (window.innerWidth <= 768) {
        sidebar?.classList.remove('open');
    }
}

/**
 * Toggle sidebar (mobile)
 */
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    sidebar?.classList.toggle('open');
    
    // Add animation
    animateElement(sidebar, 'slide-in-left');
}

/**
 * Toggle global search overlay
 */
function toggleGlobalSearch() {
    const overlay = document.getElementById('globalSearchOverlay');
    const input = document.getElementById('globalSearchInput');
    
    if (overlay.classList.contains('active')) {
        overlay.classList.remove('active');
        overlay.classList.add('overlay-exit');
        
        setTimeout(() => {
            overlay.style.display = 'none';
            overlay.classList.remove('overlay-exit');
        }, 300);
    } else {
        overlay.style.display = 'flex';
        overlay.classList.add('active', 'overlay-enter');
        input?.focus();
        
        setTimeout(() => {
            overlay.classList.remove('overlay-enter');
        }, 300);
    }
}

/**
 * Perform global search
 */
async function performGlobalSearch() {
    const input = document.getElementById('globalSearchInput');
    const results = document.getElementById('searchResults');
    const query = input?.value?.trim();
    
    if (!query) return;
    
    // Show loading state
    showSearchLoading(results);
    
    try {
        const searchData = {
            query: query,
            use_ai_agent: window.VerdureApp.searchAIEnabled,
            filters: getSearchFilters()
        };
        
        const response = await fetch('/api/semantic_search', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(searchData)
        });
        
        const data = await response.json();
        
        if (data.success) {
            displaySearchResults(data.results, results);
        } else {
            showError('Search failed: ' + (data.error || 'Unknown error'));
        }
        
    } catch (error) {
        console.error('Search error:', error);
        showError('Search failed. Please try again.');
    }
}

/**
 * Show search loading state
 */
function showSearchLoading(container) {
    container.innerHTML = `
        <div class="search-loading">
            <div class="processing-spinner"></div>
            <p>Searching your content...</p>
        </div>
    `;
    animateElement(container, 'fade-in');
}

/**
 * Display search results
 */
function displaySearchResults(results, container) {
    if (!results || results.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <i class="fas fa-search"></i>
                <p>No results found</p>
            </div>
        `;
        return;
    }
    
    const resultsHTML = results.map((result, index) => `
        <div class="search-result-item stagger-item" onclick="openSearchResult('${result.id}')">
            <div class="search-result-title">${escapeHtml(result.metadata?.title || 'Untitled')}</div>
            <div class="search-result-content">${escapeHtml(truncateText(result.content, 150))}</div>
            <div class="search-result-meta">
                <span class="search-result-type">${escapeHtml(result.metadata?.content_type || 'Unknown')}</span>
                <span class="search-result-score">Score: ${Math.round((1 - (result.distance || 0)) * 100)}%</span>
            </div>
        </div>
    `).join('');
    
    container.innerHTML = `<div class="stagger-container">${resultsHTML}</div>`;
    animateElement(container, 'fade-in');
}

/**
 * Get current search filters
 */
function getSearchFilters() {
    // This would be implemented based on filter UI
    return {};
}

/**
 * Open search result
 */
function openSearchResult(contentId) {
    // Navigate to content or show in modal
    console.log('Opening content:', contentId);
    // Implementation depends on content type
}

/**
 * Toggle AI search mode
 */
function toggleAISearch() {
    window.VerdureApp.searchAIEnabled = !window.VerdureApp.searchAIEnabled;
    
    const toggleBtn = document.getElementById('aiSearchToggle');
    if (toggleBtn) {
        if (window.VerdureApp.searchAIEnabled) {
            toggleBtn.classList.add('active');
            showNotification('AI-powered search enabled', 'success');
        } else {
            toggleBtn.classList.remove('active');
            showNotification('AI-powered search disabled', 'info');
        }
    }
}

/**
 * Close all overlays and modals
 */
function closeAllOverlays() {
    // Close global search
    const searchOverlay = document.getElementById('globalSearchOverlay');
    if (searchOverlay?.classList.contains('active')) {
        toggleGlobalSearch();
    }
    
    // Close all modals
    const modals = document.querySelectorAll('.modal.active');
    modals.forEach(modal => {
        closeModal(modal.id);
    });
    
    // Close processing overlay
    hideProcessingOverlay();
}

/**
 * Show/hide modal
 */
function showModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.style.display = 'flex';
        modal.classList.add('active', 'modal-enter');
        
        setTimeout(() => {
            modal.classList.remove('modal-enter');
        }, 400);
    }
}

function closeModal(modalId) {
    const modal = document.getElementById(modalId);
    if (modal) {
        modal.classList.add('modal-exit');
        
        setTimeout(() => {
            modal.style.display = 'none';
            modal.classList.remove('active', 'modal-exit');
        }, 300);
    }
}

/**
 * Show/hide processing overlay
 */
function showProcessingOverlay(title = 'Processing...', message = 'Please wait while AI analyzes your input') {
    const overlay = document.getElementById('processingOverlay');
    const titleEl = document.getElementById('processingTitle');
    const messageEl = document.getElementById('processingMessage');
    
    if (titleEl) titleEl.textContent = title;
    if (messageEl) messageEl.textContent = message;
    
    if (overlay) {
        overlay.style.display = 'flex';
        overlay.classList.add('active');
        window.VerdureApp.processing = true;
    }
}

function hideProcessingOverlay() {
    const overlay = document.getElementById('processingOverlay');
    if (overlay) {
        overlay.classList.remove('active');
        setTimeout(() => {
            overlay.style.display = 'none';
        }, 300);
        window.VerdureApp.processing = false;
    }
}

/**
 * Notification system
 */
function showNotification(message, type = 'info', duration = 5000) {
    const container = document.getElementById('notificationContainer');
    if (!container) return;
    
    const notificationId = 'notification-' + Date.now();
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.id = notificationId;
    
    notification.innerHTML = `
        <div class="notification-header">
            <span class="notification-title">${getNotificationTitle(type)}</span>
            <button class="notification-close" onclick="closeNotification('${notificationId}')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        <div class="notification-message">${escapeHtml(message)}</div>
    `;
    
    container.appendChild(notification);
    
    // Auto-dismiss after duration
    if (duration > 0) {
        setTimeout(() => {
            closeNotification(notificationId);
        }, duration);
    }
    
    // Add to app state
    window.VerdureApp.notifications.push({
        id: notificationId,
        message,
        type,
        timestamp: new Date()
    });
}

function getNotificationTitle(type) {
    const titles = {
        success: 'Success',
        error: 'Error',
        warning: 'Warning',
        info: 'Info'
    };
    return titles[type] || 'Notification';
}

function closeNotification(notificationId) {
    const notification = document.getElementById(notificationId);
    if (notification) {
        notification.classList.add('notification-exit');
        setTimeout(() => {
            notification.remove();
        }, 300);
    }
    
    // Remove from app state
    window.VerdureApp.notifications = window.VerdureApp.notifications.filter(
        n => n.id !== notificationId
    );
}

/**
 * Show error message
 */
function showError(message) {
    showNotification(message, 'error');
    console.error('Verdure Error:', message);
}

/**
 * Show success message
 */
function showSuccess(message) {
    showNotification(message, 'success');
}

/**
 * Check AI models status
 */
async function checkAIModelsStatus() {
    const deepseekStatus = document.getElementById('deepseekStatus');
    const geminiStatus = document.getElementById('geminiStatus');
    const ollamaStatus = document.getElementById('ollamaStatus');
    
    // Set loading state
    [deepseekStatus, geminiStatus, ollamaStatus].forEach(el => {
        if (el) el.classList.add('loading');
    });
    
    try {
        // This would be an actual API call to check model status
        // For now, simulate the check
        setTimeout(() => {
            if (deepseekStatus) {
                deepseekStatus.classList.remove('loading');
                deepseekStatus.classList.add('online');
            }
            if (geminiStatus) {
                geminiStatus.classList.remove('loading');
                geminiStatus.classList.add('online');
            }
            if (ollamaStatus) {
                ollamaStatus.classList.remove('loading');
                ollamaStatus.classList.add('online');
            }
            
            updateAIStatus('AI Ready');
        }, 2000);
        
    } catch (error) {
        console.error('Failed to check AI status:', error);
        updateAIStatus('AI Error');
    }
}

/**
 * Update AI status indicator
 */
function updateAIStatus(status) {
    const statusEl = document.querySelector('.status-text');
    if (statusEl) {
        statusEl.textContent = status;
    }
    
    const indicator = document.querySelector('.status-indicator');
    if (indicator) {
        indicator.className = 'status-indicator';
        if (status.includes('Ready')) {
            indicator.classList.add('online');
        } else if (status.includes('Error')) {
            indicator.classList.add('offline');
        } else {
            indicator.classList.add('loading');
        }
    }
}

/**
 * Load user preferences
 */
function loadUserPreferences() {
    try {
        const preferences = localStorage.getItem('verdure_preferences');
        if (preferences) {
            const prefs = JSON.parse(preferences);
            
            // Apply preferences
            if (prefs.searchAIEnabled !== undefined) {
                window.VerdureApp.searchAIEnabled = prefs.searchAIEnabled;
            }
            
            // Update UI based on preferences
            updateUIFromPreferences(prefs);
        }
    } catch (error) {
        console.error('Failed to load user preferences:', error);
    }
}

/**
 * Save user preferences
 */
function saveUserPreferences() {
    try {
        const preferences = {
            searchAIEnabled: window.VerdureApp.searchAIEnabled,
            // Add more preferences as needed
        };
        
        localStorage.setItem('verdure_preferences', JSON.stringify(preferences));
    } catch (error) {
        console.error('Failed to save user preferences:', error);
    }
}

/**
 * Update UI from preferences
 */
function updateUIFromPreferences(prefs) {
    // Update AI search toggle
    const aiToggle = document.getElementById('aiSearchToggle');
    if (aiToggle && prefs.searchAIEnabled) {
        aiToggle.classList.add('active');
    }
}

/**
 * Show welcome animation
 */
function showWelcomeAnimation() {
    const logo = document.querySelector('.logo-icon');
    if (logo) {
        logo.style.animation = 'bounce-in 1s ease-out';
    }
    
    // Animate navigation items
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach((item, index) => {
        setTimeout(() => {
            animateElement(item, 'slide-in-left');
        }, index * 100);
    });
}

/**
 * Animate element with class
 */
function animateElement(element, animationClass) {
    if (!element) return;
    
    element.classList.add('animate-' + animationClass);
    
    // Remove animation class after animation completes
    setTimeout(() => {
        element.classList.remove('animate-' + animationClass);
    }, 1000);
}

/**
 * Utility functions
 */
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

function truncateText(text, length) {
    if (text.length <= length) return text;
    return text.substring(0, length) + '...';
}

function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Auto-save preferences when app state changes
window.addEventListener('beforeunload', saveUserPreferences);

// Export functions for global access
window.Verdure = {
    showModal,
    closeModal,
    showNotification,
    showError,
    showSuccess,
    showProcessingOverlay,
    hideProcessingOverlay,
    toggleGlobalSearch,
    toggleAISearch,
    animateElement
};
