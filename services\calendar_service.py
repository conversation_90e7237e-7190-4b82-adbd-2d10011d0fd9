"""
Calendar service for managing events and deadlines
"""

from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional
from models.database import db, CalendarEvent, Content

class CalendarService:
    """Service for calendar operations and event management"""
    
    def __init__(self):
        pass
    
    def create_event(self, title: str, start_datetime: datetime, 
                    event_type: str = 'event', **kwargs) -> CalendarEvent:
        """Create a new calendar event"""
        
        event = CalendarEvent(
            title=title,
            start_datetime=start_datetime,
            event_type=event_type,
            description=kwargs.get('description', ''),
            end_datetime=kwargs.get('end_datetime'),
            importance=kwargs.get('importance', 3),
            content_id=kwargs.get('content_id')
        )
        
        db.session.add(event)
        db.session.commit()
        
        return event
    
    def get_upcoming_events(self, days_ahead: int = 7) -> List[CalendarEvent]:
        """Get upcoming events within specified days"""
        
        start_date = datetime.now()
        end_date = start_date + timedelta(days=days_ahead)
        
        events = CalendarEvent.query.filter(
            CalendarEvent.start_datetime >= start_date,
            CalendarEvent.start_datetime <= end_date
        ).order_by(CalendarEvent.start_datetime).all()
        
        return events
    
    def get_important_dates(self, limit: int = 5) -> List[CalendarEvent]:
        """Get most important upcoming dates"""
        
        events = CalendarEvent.query.filter(
            CalendarEvent.start_datetime > datetime.now(),
            CalendarEvent.importance >= 4
        ).order_by(
            CalendarEvent.start_datetime
        ).limit(limit).all()
        
        return events
    
    def get_events_by_type(self, event_type: str, 
                          start_date: Optional[datetime] = None,
                          end_date: Optional[datetime] = None) -> List[CalendarEvent]:
        """Get events filtered by type and date range"""
        
        query = CalendarEvent.query.filter_by(event_type=event_type)
        
        if start_date:
            query = query.filter(CalendarEvent.start_datetime >= start_date)
        
        if end_date:
            query = query.filter(CalendarEvent.start_datetime <= end_date)
        
        return query.order_by(CalendarEvent.start_datetime).all()
    
    def get_calendar_data(self, year: int, month: int) -> Dict[str, Any]:
        """Get calendar data for a specific month"""
        
        # Calculate month boundaries
        start_date = datetime(year, month, 1)
        if month == 12:
            end_date = datetime(year + 1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, month + 1, 1) - timedelta(days=1)
        
        # Get events for the month
        events = CalendarEvent.query.filter(
            CalendarEvent.start_datetime >= start_date,
            CalendarEvent.start_datetime <= end_date
        ).all()
        
        # Group events by date
        events_by_date = {}
        for event in events:
            date_key = event.start_datetime.strftime('%Y-%m-%d')
            if date_key not in events_by_date:
                events_by_date[date_key] = []
            events_by_date[date_key].append(event.to_dict())
        
        return {
            'year': year,
            'month': month,
            'events_by_date': events_by_date,
            'total_events': len(events)
        }
    
    def mark_event_completed(self, event_id: int) -> bool:
        """Mark an event as completed"""
        
        try:
            event = CalendarEvent.query.get(event_id)
            if event:
                event.is_completed = True
                db.session.commit()
                return True
            return False
        except Exception:
            db.session.rollback()
            return False
    
    def update_event(self, event_id: int, **kwargs) -> Optional[CalendarEvent]:
        """Update an existing event"""
        
        try:
            event = CalendarEvent.query.get(event_id)
            if not event:
                return None
            
            # Update fields
            if 'title' in kwargs:
                event.title = kwargs['title']
            if 'description' in kwargs:
                event.description = kwargs['description']
            if 'start_datetime' in kwargs:
                event.start_datetime = kwargs['start_datetime']
            if 'end_datetime' in kwargs:
                event.end_datetime = kwargs['end_datetime']
            if 'event_type' in kwargs:
                event.event_type = kwargs['event_type']
            if 'importance' in kwargs:
                event.importance = kwargs['importance']
            
            db.session.commit()
            return event
            
        except Exception:
            db.session.rollback()
            return None
    
    def delete_event(self, event_id: int) -> bool:
        """Delete an event"""
        
        try:
            event = CalendarEvent.query.get(event_id)
            if event:
                db.session.delete(event)
                db.session.commit()
                return True
            return False
        except Exception:
            db.session.rollback()
            return False
    
    def get_deadline_reminders(self, days_ahead: int = 3) -> List[Dict[str, Any]]:
        """Get deadline reminders for upcoming due dates"""
        
        start_date = datetime.now()
        end_date = start_date + timedelta(days=days_ahead)
        
        deadlines = CalendarEvent.query.filter(
            CalendarEvent.event_type.in_(['deadline', 'exam']),
            CalendarEvent.start_datetime >= start_date,
            CalendarEvent.start_datetime <= end_date,
            CalendarEvent.is_completed == False
        ).order_by(CalendarEvent.start_datetime).all()
        
        reminders = []
        for deadline in deadlines:
            time_until = deadline.start_datetime - datetime.now()
            urgency = "high" if time_until.days <= 1 else "medium"
            
            reminders.append({
                'event': deadline.to_dict(),
                'time_until': {
                    'days': time_until.days,
                    'hours': time_until.seconds // 3600
                },
                'urgency': urgency
            })
        
        return reminders
    
    def get_study_schedule_suggestions(self) -> List[Dict[str, Any]]:
        """Get AI-suggested study schedule based on upcoming deadlines"""
        
        # Get upcoming exams and major deadlines
        upcoming_deadlines = CalendarEvent.query.filter(
            CalendarEvent.event_type.in_(['exam', 'deadline']),
            CalendarEvent.start_datetime > datetime.now(),
            CalendarEvent.importance >= 3
        ).order_by(CalendarEvent.start_datetime).limit(10).all()
        
        suggestions = []
        for deadline in upcoming_deadlines:
            days_until = (deadline.start_datetime - datetime.now()).days
            
            if days_until > 0:
                # Suggest study sessions leading up to deadline
                study_sessions = max(1, min(days_until, 7))  # 1-7 study sessions
                
                for i in range(study_sessions):
                    study_date = datetime.now() + timedelta(days=i+1)
                    suggestions.append({
                        'title': f"Study for {deadline.title}",
                        'date': study_date.strftime('%Y-%m-%d'),
                        'duration': '1-2 hours',
                        'type': 'study_session',
                        'related_deadline': deadline.to_dict()
                    })
        
        return suggestions[:10]  # Limit to 10 suggestions
