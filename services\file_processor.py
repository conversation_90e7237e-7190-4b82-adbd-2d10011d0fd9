"""
File processing service for handling uploads
"""

import os
import asyncio
from typing import Dict, Any, Optional
from werkzeug.utils import secure_filename
import PyPDF2
import docx
from PIL import Image
import mimetypes

class FileProcessor:
    """Service for processing uploaded files"""
    
    def __init__(self):
        self.supported_types = {
            'pdf': self._process_pdf,
            'doc': self._process_doc,
            'docx': self._process_docx,
            'txt': self._process_text,
            'jpg': self._process_image,
            'jpeg': self._process_image,
            'png': self._process_image,
            'gif': self._process_image,
            'ppt': self._process_presentation,
            'pptx': self._process_presentation
        }
    
    async def process_file(self, file_path: str) -> Dict[str, Any]:
        """Process uploaded file and extract content"""
        
        try:
            # Get file info
            filename = os.path.basename(file_path)
            file_ext = filename.split('.')[-1].lower()
            file_size = os.path.getsize(file_path)
            mime_type, _ = mimetypes.guess_type(file_path)
            
            # Process based on file type
            if file_ext in self.supported_types:
                content = await self.supported_types[file_ext](file_path)
            else:
                content = f"Unsupported file type: {file_ext}"
            
            return {
                'filename': filename,
                'type': file_ext,
                'size': file_size,
                'mime_type': mime_type,
                'text': content,
                'status': 'processed'
            }
            
        except Exception as e:
            return {
                'filename': os.path.basename(file_path) if file_path else 'unknown',
                'type': 'unknown',
                'size': 0,
                'text': f"Error processing file: {str(e)}",
                'status': 'error'
            }
    
    async def _process_pdf(self, file_path: str) -> str:
        """Extract text from PDF files"""
        
        try:
            text = ""
            with open(file_path, 'rb') as file:
                pdf_reader = PyPDF2.PdfReader(file)
                
                for page_num in range(len(pdf_reader.pages)):
                    page = pdf_reader.pages[page_num]
                    text += page.extract_text() + "\n"
            
            return text.strip() if text.strip() else "No text found in PDF"
            
        except Exception as e:
            return f"Error reading PDF: {str(e)}"
    
    async def _process_docx(self, file_path: str) -> str:
        """Extract text from DOCX files"""
        
        try:
            doc = docx.Document(file_path)
            text = ""
            
            for paragraph in doc.paragraphs:
                text += paragraph.text + "\n"
            
            return text.strip() if text.strip() else "No text found in document"
            
        except Exception as e:
            return f"Error reading DOCX: {str(e)}"
    
    async def _process_doc(self, file_path: str) -> str:
        """Process DOC files (basic implementation)"""
        
        # DOC files require more complex processing
        # For now, return a placeholder
        return "DOC file processing not fully implemented yet. Please convert to DOCX format."
    
    async def _process_text(self, file_path: str) -> str:
        """Read plain text files"""
        
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            return content if content.strip() else "Empty text file"
            
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                with open(file_path, 'r', encoding='latin-1') as file:
                    content = file.read()
                return content if content.strip() else "Empty text file"
            except Exception as e:
                return f"Error reading text file: {str(e)}"
        except Exception as e:
            return f"Error reading text file: {str(e)}"
    
    async def _process_image(self, file_path: str) -> str:
        """Process image files"""
        
        try:
            # Basic image info
            with Image.open(file_path) as img:
                width, height = img.size
                format_type = img.format
                mode = img.mode
            
            # For now, return image metadata
            # In the future, this would use OCR or AI vision
            return f"Image file: {format_type}, {width}x{height}, Mode: {mode}\nOCR/Vision processing not yet implemented."
            
        except Exception as e:
            return f"Error processing image: {str(e)}"
    
    async def _process_presentation(self, file_path: str) -> str:
        """Process PowerPoint files"""
        
        try:
            if file_path.endswith('.pptx'):
                return await self._process_pptx(file_path)
            else:
                return "PPT file processing not fully implemented. Please convert to PPTX format."
                
        except Exception as e:
            return f"Error processing presentation: {str(e)}"
    
    async def _process_pptx(self, file_path: str) -> str:
        """Extract text from PPTX files"""
        
        try:
            from pptx import Presentation
            
            prs = Presentation(file_path)
            text = ""
            
            for slide_num, slide in enumerate(prs.slides, 1):
                text += f"\n--- Slide {slide_num} ---\n"
                
                for shape in slide.shapes:
                    if hasattr(shape, "text"):
                        text += shape.text + "\n"
            
            return text.strip() if text.strip() else "No text found in presentation"
            
        except ImportError:
            return "python-pptx library not installed. Cannot process PPTX files."
        except Exception as e:
            return f"Error reading PPTX: {str(e)}"
    
    def get_file_type_info(self, filename: str) -> Dict[str, Any]:
        """Get information about file type and processing capabilities"""
        
        file_ext = filename.split('.')[-1].lower()
        
        file_types = {
            'pdf': {'category': 'document', 'description': 'PDF Document', 'ai_processable': True},
            'docx': {'category': 'document', 'description': 'Word Document', 'ai_processable': True},
            'doc': {'category': 'document', 'description': 'Word Document (Legacy)', 'ai_processable': False},
            'txt': {'category': 'document', 'description': 'Text File', 'ai_processable': True},
            'pptx': {'category': 'presentation', 'description': 'PowerPoint Presentation', 'ai_processable': True},
            'ppt': {'category': 'presentation', 'description': 'PowerPoint (Legacy)', 'ai_processable': False},
            'jpg': {'category': 'image', 'description': 'JPEG Image', 'ai_processable': False},
            'jpeg': {'category': 'image', 'description': 'JPEG Image', 'ai_processable': False},
            'png': {'category': 'image', 'description': 'PNG Image', 'ai_processable': False},
            'gif': {'category': 'image', 'description': 'GIF Image', 'ai_processable': False}
        }
        
        return file_types.get(file_ext, {
            'category': 'unknown',
            'description': 'Unknown File Type',
            'ai_processable': False
        })
    
    def validate_file(self, filename: str, max_size: int = 16 * 1024 * 1024) -> Dict[str, Any]:
        """Validate uploaded file"""
        
        file_ext = filename.split('.')[-1].lower()
        file_info = self.get_file_type_info(filename)
        
        errors = []
        
        if file_ext not in self.supported_types:
            errors.append(f"File type '{file_ext}' is not supported")
        
        if not filename:
            errors.append("No filename provided")
        
        return {
            'valid': len(errors) == 0,
            'errors': errors,
            'file_info': file_info
        }
