"""
Input categorization service using AI
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from models.database import db, Content, CalendarEvent
from services.openrouter_client import OpenRouterClient

class InputCategorizer:
    """Service for AI-powered input categorization and processing"""
    
    def __init__(self, openrouter_client: OpenRouterClient):
        self.openrouter_client = openrouter_client
        
    async def categorize_input(self, text: str, files: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """Categorize user input using AI"""
        
        # Use OpenRouter client for AI categorization
        result = await self.openrouter_client.categorize_input(text, files)
        
        return {
            'category': result.get('category', 'QUESTION'),
            'confidence': result.get('confidence', 0),
            'metadata': result.get('metadata', {})
        }
    
    async def process_content(self, text: str, category: str, files: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """Process content based on its category"""
        
        try:
            # Create base content entry
            content = Content(
                title=self._extract_title(text, category),
                content_type=category.lower(),
                content=text,
                ai_summary=await self.openrouter_client.generate_summary(text, category)
            )
            
            # Process based on category
            if category == 'TASK':
                await self._process_task(content, text)
            elif category == 'REMINDER':
                await self._process_reminder(content, text)
            elif category == 'EVENT':
                await self._process_event(content, text)
            elif category == 'LECTURE_SLIDES':
                await self._process_lecture_slides(content, text, files)
            elif category == 'STUDY_GUIDE':
                await self._process_study_guide(content, text)
            elif category == 'QUESTION':
                await self._process_question(content, text)
            
            # Save to database
            db.session.add(content)
            db.session.commit()
            
            return {
                'id': content.id,
                'title': content.title,
                'category': category,
                'metadata': content.get_metadata(),
                'summary': content.ai_summary
            }
            
        except Exception as e:
            db.session.rollback()
            raise Exception(f"Content processing failed: {str(e)}")
    
    def _extract_title(self, text: str, category: str) -> str:
        """Extract appropriate title from text based on category"""
        
        # Take first 50 characters or first sentence, whichever is shorter
        first_sentence = text.split('.')[0] if '.' in text else text
        title = first_sentence[:50].strip()
        
        if not title:
            title = f"New {category.title()}"
        
        return title
    
    async def _process_task(self, content: Content, text: str):
        """Process task-type content"""
        
        # Extract due date and priority using AI
        metadata = await self._extract_task_metadata(text)
        content.set_metadata(metadata)
        
        # Create calendar event if due date is found
        if metadata.get('due_date'):
            try:
                due_date = datetime.fromisoformat(metadata['due_date'])
                event = CalendarEvent(
                    title=f"Task Due: {content.title}",
                    description=text,
                    start_datetime=due_date,
                    event_type='deadline',
                    importance=metadata.get('priority_level', 3)
                )
                content.calendar_events.append(event)
            except ValueError:
                pass  # Invalid date format
    
    async def _process_reminder(self, content: Content, text: str):
        """Process reminder-type content"""
        
        metadata = await self._extract_reminder_metadata(text)
        content.set_metadata(metadata)
        
        # Create calendar event for reminder
        if metadata.get('reminder_date'):
            try:
                reminder_date = datetime.fromisoformat(metadata['reminder_date'])
                event = CalendarEvent(
                    title=f"Reminder: {content.title}",
                    description=text,
                    start_datetime=reminder_date,
                    event_type='reminder',
                    importance=metadata.get('priority_level', 2)
                )
                content.calendar_events.append(event)
            except ValueError:
                pass
    
    async def _process_event(self, content: Content, text: str):
        """Process event-type content"""
        
        metadata = await self._extract_event_metadata(text)
        content.set_metadata(metadata)
        
        # Create calendar event
        if metadata.get('start_date'):
            try:
                start_date = datetime.fromisoformat(metadata['start_date'])
                end_date = None
                if metadata.get('end_date'):
                    end_date = datetime.fromisoformat(metadata['end_date'])
                
                event = CalendarEvent(
                    title=content.title,
                    description=text,
                    start_datetime=start_date,
                    end_datetime=end_date,
                    event_type=metadata.get('event_type', 'event'),
                    importance=metadata.get('priority_level', 3)
                )
                content.calendar_events.append(event)
            except ValueError:
                pass
    
    async def _process_lecture_slides(self, content: Content, text: str, files: Optional[List[Dict]]):
        """Process lecture slides content"""
        
        metadata = {'content_type': 'lecture_slides'}
        
        if files:
            metadata['files'] = files
            # Process each file if needed
            for file_info in files:
                if file_info.get('type') in ['pdf', 'ppt', 'pptx']:
                    # File processing would happen here
                    metadata['processed_files'] = metadata.get('processed_files', [])
                    metadata['processed_files'].append(file_info)
        
        content.set_metadata(metadata)
    
    async def _process_study_guide(self, content: Content, text: str):
        """Process study guide content"""
        
        metadata = {'content_type': 'study_guide'}
        
        # Extract key topics and concepts using AI
        topics = await self._extract_study_topics(text)
        metadata['topics'] = topics
        
        content.set_metadata(metadata)
    
    async def _process_question(self, content: Content, text: str):
        """Process question-type content"""
        
        metadata = {'content_type': 'question'}
        
        # Analyze question type and subject
        question_analysis = await self._analyze_question(text)
        metadata.update(question_analysis)
        
        content.set_metadata(metadata)
    
    async def _extract_task_metadata(self, text: str) -> Dict[str, Any]:
        """Extract task-specific metadata using AI"""
        
        system_prompt = """Extract task metadata from the text. Return JSON with:
        {
            "due_date": "ISO date if mentioned",
            "priority_level": 1-5,
            "estimated_duration": "time estimate",
            "subject": "course/subject if mentioned",
            "task_type": "assignment/homework/project/study"
        }"""
        
        try:
            # This would use the AI client to extract metadata
            # For now, return basic metadata
            return {
                "priority_level": 3,
                "task_type": "general",
                "extracted_from": "user_input"
            }
        except Exception:
            return {"priority_level": 3}
    
    async def _extract_reminder_metadata(self, text: str) -> Dict[str, Any]:
        """Extract reminder-specific metadata"""
        
        return {
            "reminder_type": "general",
            "priority_level": 2
        }
    
    async def _extract_event_metadata(self, text: str) -> Dict[str, Any]:
        """Extract event-specific metadata"""
        
        return {
            "event_type": "general",
            "priority_level": 3
        }
    
    async def _extract_study_topics(self, text: str) -> List[str]:
        """Extract study topics from text"""
        
        # This would use AI to extract topics
        # For now, return basic topics
        words = text.split()
        topics = [word for word in words if len(word) > 3][:5]
        return topics
    
    async def _analyze_question(self, text: str) -> Dict[str, Any]:
        """Analyze question content"""
        
        return {
            "question_type": "general",
            "subject": "unknown",
            "complexity": "medium"
        }
    
    async def generate_study_guide(self, course_content: List[Content]) -> Dict[str, Any]:
        """Generate comprehensive study guide from course content"""
        
        # Convert content to format expected by AI client
        content_data = []
        for content in course_content:
            content_data.append({
                'title': content.title,
                'content_type': content.content_type,
                'content': content.content,
                'ai_summary': content.ai_summary
            })
        
        # Use AI to generate study guide
        study_guide = await self.openrouter_client.generate_study_guide(content_data)
        
        return study_guide
