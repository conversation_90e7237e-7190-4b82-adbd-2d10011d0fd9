"""
OpenRouter API client for AI model integration
"""

import asyncio
import aiohttp
import json
from typing import Dict, List, Optional, Any
from config import Config

class OpenRouterClient:
    """Client for interacting with OpenRouter API"""
    
    def __init__(self):
        self.api_key = Config.OPENROUTER_API_KEY
        self.base_url = Config.OPENROUTER_BASE_URL
        self.deepseek_model = Config.DEEPSEEK_MODEL
        self.gemini_model = Config.GEMINI_MODEL
        
    async def _make_request(self, endpoint: str, data: Dict[str, Any], model: str) -> Dict[str, Any]:
        """Make async request to OpenRouter API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "http://localhost:5000",
            "X-Title": "Verdure AI Student Assistant"
        }
        
        payload = {
            "model": model,
            **data
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                f"{self.base_url}/{endpoint}",
                headers=headers,
                json=payload
            ) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"OpenRouter API error {response.status}: {error_text}")
    
    async def categorize_input(self, text: str, file_info: Optional[List[Dict]] = None) -> Dict[str, Any]:
        """Use DeepSeek R1 to categorize user input"""
        
        system_prompt = """You are an intelligent categorization system for a student assistant app called Verdure. 
        Analyze the user input and categorize it into one of these types:
        
        - TASK: Action items, assignments, homework, things to do
        - REMINDER: Time-based notifications, deadlines, "remember to..."
        - EVENT: Scheduled activities, meetings, exams, classes with specific times
        - LECTURE_SLIDES: Uploaded presentation files (PDF, PPT, etc.)
        - STUDY_GUIDE: Study materials, notes, guides, reference documents
        - QUESTION: Questions about previously indexed content or academic topics
        
        Also extract any relevant metadata like:
        - Due dates/times (if mentioned)
        - Course names (if mentioned)
        - Priority level (high/medium/low)
        - Tags/keywords
        
        Return your response as a JSON object with:
        {
            "category": "CATEGORY_NAME",
            "confidence": 95,
            "metadata": {
                "due_date": "2025-07-15",
                "course": "Biology 101",
                "priority": "high",
                "tags": ["exam", "chapter5"],
                "reasoning": "Brief explanation of categorization"
            }
        }"""
        
        user_message = f"Text: {text}"
        if file_info:
            user_message += f"\nFiles: {json.dumps(file_info)}"
        
        data = {
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_message}
            ],
            "temperature": 0.1,
            "max_tokens": 500
        }
        
        try:
            response = await self._make_request("chat/completions", data, self.deepseek_model)
            content = response['choices'][0]['message']['content']
            
            # Try to parse JSON response
            try:
                result = json.loads(content)
                return result
            except json.JSONDecodeError:
                # Fallback if JSON parsing fails
                return {
                    "category": "QUESTION",
                    "confidence": 50,
                    "metadata": {
                        "reasoning": "Failed to parse AI response, defaulting to QUESTION",
                        "raw_response": content
                    }
                }
                
        except Exception as e:
            # Fallback categorization
            return {
                "category": "QUESTION",
                "confidence": 0,
                "metadata": {
                    "error": str(e),
                    "reasoning": "API error, using fallback categorization"
                }
            }
    
    async def generate_summary(self, content: str, content_type: str) -> str:
        """Generate AI summary of content"""
        
        system_prompt = f"""You are an expert at creating concise, helpful summaries for students. 
        Create a clear, well-structured summary of the following {content_type} content.
        
        Guidelines:
        - Keep it concise but comprehensive
        - Use bullet points for key information
        - Highlight important concepts, dates, and requirements
        - Make it study-friendly and easy to review
        """
        
        data = {
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Please summarize this {content_type}:\n\n{content}"}
            ],
            "temperature": 0.3,
            "max_tokens": 800
        }
        
        try:
            response = await self._make_request("chat/completions", data, self.deepseek_model)
            return response['choices'][0]['message']['content']
        except Exception as e:
            return f"Summary generation failed: {str(e)}"
    
    async def generate_study_guide(self, course_content: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate comprehensive study guide from course content"""
        
        # Prepare content for AI analysis
        content_text = ""
        for item in course_content:
            content_text += f"\n--- {item.get('title', 'Untitled')} ({item.get('content_type', 'unknown')}) ---\n"
            content_text += item.get('content', '') + "\n"
            if item.get('ai_summary'):
                content_text += f"Summary: {item.get('ai_summary')}\n"
        
        system_prompt = """You are an expert educational content creator. Generate a comprehensive study guide 
        from the provided course materials. Structure it as follows:
        
        1. Course Overview
        2. Key Topics & Concepts
        3. Important Dates & Deadlines
        4. Study Recommendations
        5. Practice Questions/Review Items
        
        Make it well-organized, easy to follow, and optimized for effective studying."""
        
        data = {
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Create a study guide from this course content:\n{content_text}"}
            ],
            "temperature": 0.4,
            "max_tokens": 2000
        }
        
        try:
            response = await self._make_request("chat/completions", data, self.deepseek_model)
            study_guide_text = response['choices'][0]['message']['content']
            
            return {
                "content": study_guide_text,
                "generated_at": "2025-06-30T00:00:00Z",
                "source_items": len(course_content)
            }
        except Exception as e:
            return {
                "content": f"Study guide generation failed: {str(e)}",
                "generated_at": "2025-06-30T00:00:00Z",
                "source_items": 0,
                "error": True
            }
    
    async def process_image_content(self, image_path: str, context: str = "") -> str:
        """Use Gemini Vision to extract text/content from images"""
        
        # Note: This would require proper image encoding for API
        # For now, returning a placeholder implementation
        system_prompt = """You are an expert at analyzing academic images including:
        - Lecture slides and presentations
        - Handwritten notes
        - Diagrams and charts
        - Textbook pages
        
        Extract all text content and describe any visual elements like diagrams, charts, or equations."""
        
        # This would need proper image encoding implementation
        try:
            # Placeholder for image processing
            return f"Image processing not yet implemented for {image_path}. Context: {context}"
        except Exception as e:
            return f"Image processing failed: {str(e)}"
    
    async def intelligent_search(self, query: str, available_content: List[Dict]) -> Dict[str, Any]:
        """Use AI to perform intelligent search analysis"""
        
        system_prompt = """You are an intelligent search agent for a student assistant app.
        
        Given a search query and available content, analyze the query intent and provide:
        1. Search strategy recommendations
        2. Content filtering suggestions
        3. Result ranking criteria
        4. Related queries the user might be interested in
        
        Return as JSON with your analysis and recommendations."""
        
        content_summary = f"Available content types: {set(item.get('content_type') for item in available_content)}"
        content_summary += f"\nTotal items: {len(available_content)}"
        
        data = {
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Search query: {query}\n\nContent summary: {content_summary}"}
            ],
            "temperature": 0.3,
            "max_tokens": 600
        }
        
        try:
            response = await self._make_request("chat/completions", data, self.deepseek_model)
            content = response['choices'][0]['message']['content']
            
            try:
                return json.loads(content)
            except json.JSONDecodeError:
                return {
                    "strategy": "keyword_match",
                    "suggestions": [],
                    "related_queries": [],
                    "raw_response": content
                }
                
        except Exception as e:
            return {
                "strategy": "fallback",
                "error": str(e),
                "suggestions": [],
                "related_queries": []
            }
