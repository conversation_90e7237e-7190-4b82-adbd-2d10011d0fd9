{% extends "base.html" %}

{% block title %}Courses - Verdure{% endblock %}
{% block page_title %}Courses{% endblock %}

{% block content %}
<div class="courses-layout">
    <!-- Courses Header -->
    <section class="courses-header">
        <div class="header-content">
            <div class="header-text">
                <h1>Your Courses</h1>
                <p>Organize and manage your academic content with AI-powered insights</p>
            </div>
            
            <div class="header-actions">
                <button class="btn btn-outline" onclick="showImportModal()">
                    <i class="fas fa-file-import"></i>
                    Import Course
                </button>
                
                <button class="btn btn-primary" onclick="showCreateCourseModal()">
                    <i class="fas fa-plus"></i>
                    Create Course
                </button>
            </div>
        </div>
    </section>
    
    <!-- Courses Grid -->
    <section class="courses-grid-section">
        {% if courses %}
        <div class="courses-grid">
            {% for course in courses %}
            <div class="course-card hover-lift" 
                 data-course-id="{{ course.id }}"
                 style="border-top: 4px solid {{ course.color }}">
                
                <div class="course-header">
                    <div class="course-info">
                        <h3 class="course-name">{{ course.name }}</h3>
                        {% if course.code %}
                        <span class="course-code">{{ course.code }}</span>
                        {% endif %}
                    </div>
                    
                    <div class="course-actions">
                        <button class="action-btn" 
                                onclick="generateStudyGuide({{ course.id }})"
                                title="Generate AI Study Guide">
                            <i class="fas fa-brain"></i>
                        </button>
                        
                        <button class="action-btn" 
                                onclick="showCourseOptions({{ course.id }})"
                                title="Course Options">
                            <i class="fas fa-ellipsis-v"></i>
                        </button>
                    </div>
                </div>
                
                {% if course.description %}
                <div class="course-description">
                    <p>{{ course.description }}</p>
                </div>
                {% endif %}
                
                <div class="course-stats">
                    <div class="stat-item">
                        <i class="fas fa-file-alt"></i>
                        <span>{{ course.content|length }} Content Items</span>
                    </div>
                    
                    <div class="stat-item">
                        <i class="fas fa-calendar"></i>
                        <span>Created {{ course.created_at.strftime('%b %d, %Y') }}</span>
                    </div>
                </div>
                
                <div class="course-progress">
                    <div class="progress-info">
                        <span>Course Progress</span>
                        <span class="progress-percentage">{{ ((course.content|selectattr('content_type', 'equalto', 'task')|selectattr('metadata.completed', 'equalto', true)|list|length / (course.content|selectattr('content_type', 'equalto', 'task')|list|length or 1)) * 100)|round }}%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" 
                             style="width: {{ ((course.content|selectattr('content_type', 'equalto', 'task')|selectattr('metadata.completed', 'equalto', true)|list|length / (course.content|selectattr('content_type', 'equalto', 'task')|list|length or 1)) * 100)|round }}%"></div>
                    </div>
                </div>
                
                <div class="course-footer">
                    <button class="btn btn-outline btn-sm" onclick="openCourse({{ course.id }})">
                        <i class="fas fa-eye"></i>
                        View Course
                    </button>
                    
                    <button class="btn btn-primary btn-sm" onclick="addContent({{ course.id }})">
                        <i class="fas fa-plus"></i>
                        Add Content
                    </button>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="empty-courses-state">
            <div class="empty-illustration">
                <i class="fas fa-graduation-cap"></i>
            </div>
            <h2>No Courses Yet</h2>
            <p>Create your first course to start organizing your academic content with AI assistance</p>
            
            <div class="empty-actions">
                <button class="btn btn-primary btn-lg" onclick="showCreateCourseModal()">
                    <i class="fas fa-plus"></i>
                    Create Your First Course
                </button>
                
                <button class="btn btn-outline btn-lg" onclick="showSampleCourses()">
                    <i class="fas fa-eye"></i>
                    View Sample Courses
                </button>
            </div>
        </div>
        {% endif %}
    </section>
</div>

<!-- Create Course Modal -->
<div class="modal" id="createCourseModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Create New Course</h3>
            <button class="close-btn" onclick="closeModal('createCourseModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body">
            <form id="createCourseForm" onsubmit="createCourse(event)">
                <div class="form-group">
                    <label for="courseName">Course Name *</label>
                    <input type="text" 
                           id="courseName" 
                           name="name" 
                           required 
                           placeholder="e.g., Introduction to Biology"
                           autocomplete="off">
                </div>
                
                <div class="form-group">
                    <label for="courseCode">Course Code</label>
                    <input type="text" 
                           id="courseCode" 
                           name="code" 
                           placeholder="e.g., BIO101"
                           autocomplete="off">
                </div>
                
                <div class="form-row">
                    <div class="form-group">
                        <label for="courseColor">Course Color</label>
                        <div class="color-picker-container">
                            <input type="color" 
                                   id="courseColor" 
                                   name="color" 
                                   value="#3498db">
                            <div class="color-presets">
                                <button type="button" class="color-preset" data-color="#3498db" style="background: #3498db"></button>
                                <button type="button" class="color-preset" data-color="#2ecc71" style="background: #2ecc71"></button>
                                <button type="button" class="color-preset" data-color="#e74c3c" style="background: #e74c3c"></button>
                                <button type="button" class="color-preset" data-color="#f39c12" style="background: #f39c12"></button>
                                <button type="button" class="color-preset" data-color="#9b59b6" style="background: #9b59b6"></button>
                                <button type="button" class="color-preset" data-color="#1abc9c" style="background: #1abc9c"></button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="form-group">
                    <label for="courseDescription">Description</label>
                    <textarea id="courseDescription" 
                              name="description" 
                              rows="3" 
                              placeholder="Brief description of the course (optional)"></textarea>
                </div>
                
                <div class="form-group">
                    <label class="checkbox-label">
                        <input type="checkbox" id="generateSampleContent" name="generateSample">
                        <span class="checkmark"></span>
                        Generate sample content with AI
                    </label>
                    <small class="form-hint">This will create some example content to help you get started</small>
                </div>
            </form>
        </div>
        
        <div class="modal-footer">
            <button type="button" class="btn btn-secondary" onclick="closeModal('createCourseModal')">
                Cancel
            </button>
            <button type="submit" form="createCourseForm" class="btn btn-primary">
                <i class="fas fa-plus"></i>
                Create Course
            </button>
        </div>
    </div>
</div>

<!-- Course Options Menu -->
<div class="context-menu" id="courseOptionsMenu">
    <div class="menu-item" onclick="editCourse()">
        <i class="fas fa-edit"></i>
        <span>Edit Course</span>
    </div>
    
    <div class="menu-item" onclick="duplicateCourse()">
        <i class="fas fa-copy"></i>
        <span>Duplicate Course</span>
    </div>
    
    <div class="menu-divider"></div>
    
    <div class="menu-item" onclick="exportCourse()">
        <i class="fas fa-download"></i>
        <span>Export Course</span>
    </div>
    
    <div class="menu-item" onclick="archiveCourse()">
        <i class="fas fa-archive"></i>
        <span>Archive Course</span>
    </div>
    
    <div class="menu-divider"></div>
    
    <div class="menu-item danger" onclick="deleteCourse()">
        <i class="fas fa-trash"></i>
        <span>Delete Course</span>
    </div>
</div>

<!-- Study Guide Generation Modal -->
<div class="modal" id="studyGuideModal">
    <div class="modal-content large">
        <div class="modal-header">
            <h3>AI-Generated Study Guide</h3>
            <button class="close-btn" onclick="closeModal('studyGuideModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body">
            <div class="study-guide-loading" id="studyGuideLoading">
                <div class="processing-spinner large"></div>
                <h4>Generating Study Guide...</h4>
                <p>AI is analyzing your course content and creating a comprehensive study guide</p>
            </div>
            
            <div class="study-guide-content" id="studyGuideContent" style="display: none;">
                <!-- Generated content will be inserted here -->
            </div>
        </div>
        
        <div class="modal-footer">
            <button class="btn btn-outline" onclick="closeModal('studyGuideModal')">
                Close
            </button>
            <button class="btn btn-secondary" onclick="editStudyGuide()" id="editStudyGuideBtn" style="display: none;">
                <i class="fas fa-edit"></i>
                Edit
            </button>
            <button class="btn btn-primary" onclick="saveStudyGuide()" id="saveStudyGuideBtn" style="display: none;">
                <i class="fas fa-save"></i>
                Save to Course
            </button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/courses.js') }}"></script>
<script>
    // Initialize courses page
    document.addEventListener('DOMContentLoaded', function() {
        initializeCourses();
        setupColorPickers();
        setupCourseCards();
    });
</script>
{% endblock %}
