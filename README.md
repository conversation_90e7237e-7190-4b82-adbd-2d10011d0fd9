# 🌱 Verdure - AI-Powered Student Assistant

<div align="center">

![Verdure Logo](https://via.placeholder.com/200x80/2ecc71/ffffff?text=Verdure)

**An intelligent, animated, and seamless student productivity platform powered by AI**

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![Flask](https://img.shields.io/badge/Flask-2.3+-green.svg)](https://flask.palletsprojects.com/)
[![OpenRouter](https://img.shields.io/badge/OpenRouter-AI-purple.svg)](https://openrouter.ai/)
[![Ollama](https://img.shields.io/badge/Ollama-Local%20AI-orange.svg)](https://ollama.ai/)

</div>

## ✨ Features

### 🏠 Smart Dashboard
- **AI-Powered Input Categorization**: Automatically categorizes your input into tasks, reminders, events, study materials, or questions
- **Intelligent File Processing**: Upload PDFs, presentations, and documents for AI analysis
- **Integrated Calendar**: Seamless event and deadline management
- **Semantic Search**: Find content using natural language queries

### 📚 Course Management
- **Visual Course Organization**: Color-coded course cards with progress tracking
- **AI-Generated Summaries**: Automatic content summarization for better understanding
- **Study Guide Creation**: AI-powered study guides from your course materials
- **Content Categorization**: Automatic organization of lectures, notes, and assignments

### 🔍 Advanced Search
- **Semantic Search Engine**: Powered by local Ollama embeddings for privacy
- **AI Search Agent**: Optional intelligent search assistant for complex queries
- **Content Filtering**: Filter by course, type, date, and relevance
- **Search History**: Track and revisit your previous searches

### 🎨 Beautiful Animations
- **Alive Interface**: Responsive animations that make the app feel alive
- **Processing Feedback**: Visual indicators for AI processing states
- **Smooth Transitions**: Seamless navigation between features
- **Loading States**: Engaging loading animations during AI operations

## 🚀 Quick Start

### Prerequisites

1. **Python 3.8+** - [Download Python](https://www.python.org/downloads/)
2. **Ollama** (Optional but recommended) - [Install Ollama](https://ollama.ai/)
3. **OpenRouter API Key** - [Get API Key](https://openrouter.ai/)

### Installation

1. **Clone the repository**
   ```bash
   git clone https://github.com/yourusername/verdure.git
   cd verdure
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up environment**
   ```bash
   cp .env.template .env
   ```
   Edit `.env` with your configuration:
   ```env
   OPENROUTER_API_KEY=your-openrouter-api-key-here
   OLLAMA_BASE_URL=http://localhost:11434
   SECRET_KEY=your-secret-key-here
   ```

4. **Start Ollama (Optional)**
   ```bash
   ollama serve
   ollama pull nomic-embed-text
   ```

5. **Run the application**
   ```bash
   python run.py
   ```

6. **Open your browser**
   ```
   http://localhost:5000
   ```

## 🛠️ Configuration

### OpenRouter Models

Verdure uses two primary AI models:

- **DeepSeek R1**: For intelligent categorization and reasoning
- **Gemini 2.0 Flash**: For content processing and vision capabilities

### Ollama Setup

For local embeddings and semantic search:

```bash
# Install Ollama
curl -fsSL https://ollama.ai/install.sh | sh

# Start Ollama service
ollama serve

# Pull the embedding model
ollama pull nomic-embed-text
```

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `OPENROUTER_API_KEY` | Your OpenRouter API key | Required |
| `OLLAMA_BASE_URL` | Ollama server URL | `http://localhost:11434` |
| `DATABASE_URL` | Database connection string | `sqlite:///verdure.db` |
| `SECRET_KEY` | Flask secret key | Auto-generated |
| `FLASK_DEBUG` | Enable debug mode | `true` |

## 📁 Project Structure

```
verdure/
├── app.py                 # Main Flask application
├── config.py              # Configuration management
├── run.py                 # Startup script
├── requirements.txt       # Python dependencies
├── .env.template          # Environment template
├── models/
│   ├── database.py        # SQLAlchemy models
│   └── embeddings.py      # Ollama integration
├── services/
│   ├── openrouter_client.py  # OpenRouter API client
│   ├── categorizer.py     # AI categorization service
│   ├── file_processor.py  # File processing service
│   └── calendar_service.py   # Calendar management
├── static/
│   ├── css/               # Stylesheets
│   ├── js/                # JavaScript files
│   └── assets/            # Images and icons
├── templates/             # HTML templates
└── uploads/               # Uploaded files
```

## 🎯 Usage Guide

### Adding Content

1. **Text Input**: Type anything in the smart input bar
   - "Complete math homework by Friday" → Categorized as TASK
   - "Exam on Monday at 2 PM" → Categorized as EVENT
   - "What is photosynthesis?" → Categorized as QUESTION

2. **File Upload**: Drag and drop files or click to browse
   - PDFs are processed for text extraction
   - Presentations are analyzed for content
   - Images are processed for text recognition

### Managing Courses

1. **Create Course**: Click "Add Course" and fill in details
2. **Add Content**: Associate content with specific courses
3. **Generate Study Guide**: AI creates comprehensive study materials
4. **Track Progress**: Monitor your learning journey

### Searching Content

1. **Simple Search**: Type natural language queries
2. **AI Agent**: Toggle for intelligent search assistance
3. **Filters**: Narrow results by course, type, or date
4. **Semantic**: Find conceptually related content

## 🤖 AI Features

### No Hardcoded Logic

Verdure is designed with **zero hardcoded AI logic**. All intelligence comes from:

- **OpenRouter API**: For categorization, summarization, and reasoning
- **Local Ollama**: For embeddings and semantic search
- **Dynamic Processing**: All AI operations are model-driven

### AI Categorization

The system automatically categorizes input into:

- **TASK**: Action items and assignments
- **REMINDER**: Time-based notifications
- **EVENT**: Scheduled activities
- **LECTURE_SLIDES**: Presentation materials
- **STUDY_GUIDE**: Study materials and notes
- **QUESTION**: Academic queries

### Content Processing

Each category receives specialized AI processing:

- **Tasks**: Due date extraction, priority assessment
- **Events**: Calendar integration, importance scoring
- **Files**: Content extraction, summarization
- **Questions**: Context-aware responses

## 🎨 Animation System

Verdure features an advanced animation system that makes the interface feel alive:

### Key Animation Features

- **Processing Indicators**: Visual feedback during AI operations
- **Categorization Animations**: Dynamic category badges with confidence scores
- **Search Result Animations**: Staggered result appearance
- **File Upload Animations**: Progress tracking with visual feedback
- **Hover Effects**: Depth and interactivity on cards and buttons

### Performance Optimization

- **Reduced Motion Support**: Respects user preferences
- **Efficient Animations**: Hardware-accelerated CSS transitions
- **Progressive Enhancement**: Graceful degradation for older browsers

## 🔒 Privacy & Security

### Local-First Approach

- **Local Embeddings**: Ollama runs entirely on your machine
- **Secure API**: OpenRouter for cloud AI with encrypted connections
- **Data Control**: Your data stays on your device by default

### Security Features

- **File Validation**: Secure file upload handling
- **Input Sanitization**: Protection against injection attacks
- **Environment Variables**: Secure configuration management
- **Error Handling**: Graceful failure without data exposure

## 🧪 Development

### Running in Development

```bash
# Install development dependencies
pip install -r requirements.txt

# Set debug mode
export FLASK_DEBUG=true

# Run with auto-reload
python run.py
```

### Testing

```bash
# Run tests (when available)
python -m pytest

# Check code style
flake8 .

# Type checking
mypy .
```

### Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 🗺️ Roadmap

### Current (v1.0)
- ✅ Smart dashboard with AI categorization
- ✅ Course management system
- ✅ File upload and processing
- ✅ Semantic search with Ollama
- ✅ Beautiful animations and UI

### Next (v1.1)
- 🔄 Background task processing with Celery
- 🔄 Enhanced error handling and validation
- 🔄 Mobile responsiveness
- 🔄 Export functionality

### Future (v2.0)
- 📋 Research hub with Google Scholar integration
- 📋 Collaborative features
- 📋 Advanced analytics
- 📋 Authentication system
- 📋 API for third-party integrations

## 📞 Support

### Getting Help

- **Documentation**: Check this README and code comments
- **Issues**: [Report bugs or request features](https://github.com/yourusername/verdure/issues)
- **Discussions**: [Community discussions](https://github.com/yourusername/verdure/discussions)

### Common Issues

**Q: AI categorization isn't working**
A: Check your OpenRouter API key in the `.env` file

**Q: Semantic search returns no results**
A: Ensure Ollama is running and the nomic-embed-text model is installed

**Q: File uploads fail**
A: Check file size limits and ensure the uploads directory is writable

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- **OpenRouter**: For providing access to state-of-the-art AI models
- **Ollama**: For local AI inference and embeddings
- **Flask Community**: For the excellent web framework
- **ChromaDB**: For vector database capabilities

---

<div align="center">

**Built with ❤️ for students, by students**

[🌟 Star this project](https://github.com/yourusername/verdure) | [🐛 Report an issue](https://github.com/yourusername/verdure/issues) | [💡 Request a feature](https://github.com/yourusername/verdure/issues/new)

</div>
