/* Verdure Main Stylesheet - Beautiful and Alive Interface */

/* CSS Reset and Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* CSS Custom Properties */
:root {
    /* Colors - Verdure Palette */
    --verdure-green: #2ecc71;
    --verdure-green-light: #58d68d;
    --verdure-green-dark: #27ae60;
    --deep-blue: #3498db;
    --deep-blue-light: #5dade2;
    --deep-blue-dark: #2980b9;
    --warm-orange: #f39c12;
    --warm-orange-light: #f8c471;
    --soft-red: #e74c3c;
    --charcoal: #2c3e50;
    --charcoal-light: #34495e;
    --light-gray: #ecf0f1;
    --white: #ffffff;
    --dark-gray: #7f8c8d;
    --medium-gray: #bdc3c7;
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--verdure-green), var(--deep-blue));
    --gradient-warm: linear-gradient(135deg, var(--warm-orange), var(--soft-red));
    --gradient-cool: linear-gradient(135deg, var(--deep-blue), #9b59b6);
    --gradient-soft: linear-gradient(135deg, #f8f9fa, #e9ecef);
    
    /* Shadows */
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 10px 30px rgba(0, 0, 0, 0.2);
    --shadow-glow: 0 0 20px rgba(46, 204, 113, 0.3);
    
    /* Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-bounce: all 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --transition-fast: all 0.15s ease;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-xxl: 3rem;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 2rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    --radius-full: 9999px;
    
    /* Z-index levels */
    --z-dropdown: 1000;
    --z-modal: 1050;
    --z-overlay: 1040;
    --z-tooltip: 1060;
}

/* Base Styles */
html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-family);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--charcoal);
    background: var(--gradient-soft);
    overflow-x: hidden;
}

/* App Container */
.app-container {
    display: flex;
    min-height: 100vh;
    position: relative;
}

/* Sidebar Styling */
.sidebar {
    width: 280px;
    background: var(--white);
    box-shadow: var(--shadow-medium);
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 100;
    transition: var(--transition-smooth);
    display: flex;
    flex-direction: column;
}

.sidebar-header {
    padding: var(--spacing-xl);
    border-bottom: 1px solid var(--light-gray);
}

.logo-container {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.logo-icon {
    font-size: var(--font-size-2xl);
    color: var(--verdure-green);
    animation: gentle-float 3s ease-in-out infinite;
}

.logo-text {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Navigation Menu */
.nav-menu {
    flex: 1;
    padding: var(--spacing-lg);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md) var(--spacing-lg);
    margin-bottom: var(--spacing-sm);
    border-radius: var(--radius-lg);
    color: var(--charcoal);
    text-decoration: none;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: var(--gradient-primary);
    transition: var(--transition-smooth);
    z-index: -1;
}

.nav-item:hover::before,
.nav-item.active::before {
    left: 0;
}

.nav-item:hover,
.nav-item.active {
    color: var(--white);
    transform: translateX(4px);
}

.nav-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.nav-item i {
    font-size: var(--font-size-lg);
    width: 20px;
    text-align: center;
}

.coming-soon {
    margin-left: auto;
    font-size: var(--font-size-xs);
    padding: var(--spacing-xs) var(--spacing-sm);
    background: var(--warm-orange);
    color: var(--white);
    border-radius: var(--radius-full);
}

.nav-divider {
    height: 1px;
    background: var(--light-gray);
    margin: var(--spacing-lg) 0;
}

/* AI Status Indicator */
.ai-status-container {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--light-gray);
}

.ai-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-sm) var(--spacing-md);
    background: var(--light-gray);
    border-radius: var(--radius-lg);
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--verdure-green);
    animation: pulse-dot 2s infinite;
}

.status-text {
    font-size: var(--font-size-sm);
    color: var(--charcoal-light);
}

/* Main Content Area */
.main-content {
    flex: 1;
    margin-left: 280px;
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/* Top Bar */
.top-bar {
    background: var(--white);
    padding: var(--spacing-lg) var(--spacing-xl);
    box-shadow: var(--shadow-light);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 50;
}

.sidebar-toggle {
    display: none;
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--charcoal);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.sidebar-toggle:hover {
    background: var(--light-gray);
}

.page-title h2 {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--charcoal);
}

.top-bar-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.action-btn {
    position: relative;
    background: none;
    border: none;
    font-size: var(--font-size-lg);
    color: var(--charcoal);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-full);
    transition: var(--transition-smooth);
}

.action-btn:hover {
    background: var(--light-gray);
    transform: scale(1.1);
}

.notification-badge {
    position: absolute;
    top: -2px;
    right: -2px;
    background: var(--soft-red);
    color: var(--white);
    font-size: var(--font-size-xs);
    font-weight: 600;
    padding: 2px 6px;
    border-radius: var(--radius-full);
    min-width: 18px;
    text-align: center;
}

/* Content Area */
.content-area {
    flex: 1;
    padding: var(--spacing-xl);
    background: transparent;
}

/* Dashboard Layout */
.dashboard-layout {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* Smart Input Section */
.smart-input-section {
    margin-bottom: var(--spacing-xl);
}

.input-container {
    max-width: 1000px;
    margin: 0 auto;
}

.input-wrapper {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-medium);
    transition: var(--transition-smooth);
    overflow: hidden;
}

.input-wrapper.pulse-input {
    animation: pulse-glow 2s infinite;
}

.input-area {
    padding: var(--spacing-xl);
}

.smart-input {
    width: 100%;
    min-height: 120px;
    border: none;
    outline: none;
    resize: vertical;
    font-family: var(--font-family);
    font-size: var(--font-size-lg);
    color: var(--charcoal);
    background: transparent;
    margin-bottom: var(--spacing-lg);
}

.smart-input::placeholder {
    color: var(--dark-gray);
}

.input-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    justify-content: flex-end;
}

.file-upload-btn,
.voice-input-btn,
.submit-btn {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-full);
    font-size: var(--font-size-sm);
    font-weight: 500;
    cursor: pointer;
    transition: var(--transition-smooth);
}

.file-upload-btn {
    background: var(--light-gray);
    color: var(--charcoal);
}

.file-upload-btn:hover {
    background: var(--medium-gray);
    transform: translateY(-2px);
}

.voice-input-btn {
    background: var(--warm-orange);
    color: var(--white);
    padding: var(--spacing-md);
}

.voice-input-btn:hover {
    background: var(--warm-orange-light);
    transform: scale(1.1);
}

.submit-btn {
    background: var(--gradient-primary);
    color: var(--white);
    min-width: 120px;
}

.submit-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-heavy);
}

.submit-btn:active {
    transform: translateY(0);
}

/* File Drop Zone */
.file-drop-zone {
    display: none;
    text-align: center;
    padding: var(--spacing-xxl);
    border: 2px dashed var(--medium-gray);
    margin: var(--spacing-lg) var(--spacing-xl) 0;
    border-radius: var(--radius-lg);
    color: var(--dark-gray);
    transition: var(--transition-smooth);
}

.file-drop-zone.active {
    display: block;
    border-color: var(--verdure-green);
    background: rgba(46, 204, 113, 0.05);
    color: var(--verdure-green);
}

.file-drop-zone i {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-md);
}

/* Categorization Display */
.categorization-display {
    display: none;
    padding: var(--spacing-md) var(--spacing-xl);
    background: var(--gradient-primary);
    color: var(--white);
    animation: slide-down 0.3s ease;
}

.categorization-display.show {
    display: block;
}

.category-indicator {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.category-label {
    font-weight: 600;
}

.confidence-score {
    font-size: var(--font-size-sm);
    opacity: 0.9;
}

/* Dashboard Grid */
.dashboard-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);
}

/* Dashboard Cards */
.dashboard-card {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-light);
    transition: var(--transition-smooth);
    overflow: hidden;
}

.dashboard-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-heavy);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg) var(--spacing-xl);
    border-bottom: 1px solid var(--light-gray);
    background: var(--gradient-soft);
}

.card-header h3 {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--charcoal);
}

.card-header i {
    color: var(--verdure-green);
}

.card-actions {
    display: flex;
    gap: var(--spacing-sm);
}

.add-btn,
.view-all-btn {
    background: none;
    border: none;
    font-size: var(--font-size-sm);
    color: var(--dark-gray);
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: var(--transition-fast);
}

.add-btn:hover,
.view-all-btn:hover {
    background: var(--light-gray);
    color: var(--charcoal);
}

.card-content {
    padding: var(--spacing-xl);
}

/* Course Quick Cards */
.courses-grid {
    display: grid;
    gap: var(--spacing-md);
}

.course-quick-card {
    padding: var(--spacing-lg);
    border: 1px solid var(--light-gray);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: var(--transition-smooth);
}

.course-quick-card:hover {
    transform: translateX(4px);
    box-shadow: var(--shadow-medium);
}

.course-info h4 {
    font-size: var(--font-size-lg);
    font-weight: 600;
    margin-bottom: var(--spacing-xs);
}

.course-code {
    font-size: var(--font-size-sm);
    color: var(--dark-gray);
    background: var(--light-gray);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
}

.content-count {
    font-size: var(--font-size-sm);
    color: var(--dark-gray);
    margin-top: var(--spacing-sm);
    display: block;
}

/* Activity Timeline */
.activity-timeline {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.activity-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    border-radius: var(--radius-lg);
    transition: var(--transition-fast);
}

.activity-item:hover {
    background: var(--light-gray);
}

.activity-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: var(--font-size-sm);
    color: var(--white);
    flex-shrink: 0;
}

.activity-icon.task { background: var(--deep-blue); }
.activity-icon.reminder { background: var(--warm-orange); }
.activity-icon.event { background: var(--verdure-green); }
.activity-icon.lecture_slides { background: var(--soft-red); }

.activity-details {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xs);
}

.activity-title {
    font-weight: 500;
}

.activity-time,
.activity-type {
    font-size: var(--font-size-sm);
    color: var(--dark-gray);
}

/* Empty States */
.empty-state {
    text-align: center;
    padding: var(--spacing-xxl);
    color: var(--dark-gray);
}

.empty-state i {
    font-size: var(--font-size-3xl);
    margin-bottom: var(--spacing-md);
    color: var(--medium-gray);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
    }
    
    .sidebar.open {
        transform: translateX(0);
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .sidebar-toggle {
        display: block;
    }
    
    .dashboard-grid {
        grid-template-columns: 1fr;
    }
    
    .content-area {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 480px) {
    .input-actions {
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }
    
    .file-upload-btn span,
    .submit-btn span {
        display: none;
    }
    
    .card-content {
        padding: var(--spacing-lg);
    }
}
