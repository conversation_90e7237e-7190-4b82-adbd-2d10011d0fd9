# Verdure AI Student Assistant Environment Configuration
# Copy this file to .env and fill in your values

# Flask Configuration
FLASK_DEBUG=true
SECRET_KEY=your-secret-key-here

# Database Configuration
DATABASE_URL=sqlite:///verdure.db

# OpenRouter API Configuration (Required for AI features)
# Get your API key from https://openrouter.ai/
OPENROUTER_API_KEY=your-openrouter-api-key-here

# Ollama Configuration (Required for embeddings)
# Make sure Ollama is installed and running
OLLAMA_BASE_URL=http://localhost:11434

# ChromaDB Configuration
CHROMA_PERSIST_DIRECTORY=./chroma_db

# Redis Configuration (Optional - for background tasks)
REDIS_URL=redis://localhost:6379/0

# File Upload Configuration
MAX_CONTENT_LENGTH=16777216  # 16MB in bytes

# AI Model Configuration
DEEPSEEK_MODEL=deepseek/deepseek-r1
GEMINI_MODEL=google/gemini-2.0-flash-exp

# Development Settings
LOG_LEVEL=INFO
DEVELOPMENT_MODE=true
