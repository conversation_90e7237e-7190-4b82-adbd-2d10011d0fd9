"""
Database models for Verdure application
"""

from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json

db = SQLAlchemy()

class Course(db.Model):
    """Course model for organizing content"""
    __tablename__ = 'courses'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    code = db.Column(db.String(20))
    color = db.Column(db.String(7), default='#3498db')
    description = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relationships
    content = db.relationship('Content', backref='course', lazy=True, cascade='all, delete-orphan')
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'code': self.code,
            'color': self.color,
            'description': self.description,
            'created_at': self.created_at.isoformat(),
            'content_count': len(self.content)
        }

class Content(db.Model):
    """Unified content storage for all types of student materials"""
    __tablename__ = 'content'
    
    id = db.Column(db.Integer, primary_key=True)
    course_id = db.Column(db.Integer, db.ForeignKey('courses.id'), nullable=True)
    title = db.Column(db.String(200))
    content_type = db.Column(db.String(50))  # task, reminder, event, slide, guide, note, question
    content = db.Column(db.Text)
    file_path = db.Column(db.String(500))
    metadata = db.Column(db.Text)  # JSON string for additional data
    embedding_id = db.Column(db.String(100))  # Reference to vector database
    ai_summary = db.Column(db.Text)  # AI-generated summary
    tags = db.Column(db.Text)  # Comma-separated tags
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    calendar_events = db.relationship('CalendarEvent', backref='content', lazy=True, cascade='all, delete-orphan')
    
    def get_metadata(self):
        """Get metadata as dictionary"""
        if self.metadata:
            try:
                return json.loads(self.metadata)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def set_metadata(self, metadata_dict):
        """Set metadata from dictionary"""
        self.metadata = json.dumps(metadata_dict)
    
    def get_tags_list(self):
        """Get tags as list"""
        if self.tags:
            return [tag.strip() for tag in self.tags.split(',') if tag.strip()]
        return []
    
    def set_tags_list(self, tags_list):
        """Set tags from list"""
        self.tags = ', '.join(tags_list)
    
    def to_dict(self):
        return {
            'id': self.id,
            'course_id': self.course_id,
            'title': self.title,
            'content_type': self.content_type,
            'content': self.content,
            'file_path': self.file_path,
            'metadata': self.get_metadata(),
            'ai_summary': self.ai_summary,
            'tags': self.get_tags_list(),
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat()
        }

class CalendarEvent(db.Model):
    """Calendar events for deadlines, exams, meetings, etc."""
    __tablename__ = 'calendar_events'
    
    id = db.Column(db.Integer, primary_key=True)
    content_id = db.Column(db.Integer, db.ForeignKey('content.id'), nullable=True)
    title = db.Column(db.String(200), nullable=False)
    description = db.Column(db.Text)
    start_datetime = db.Column(db.DateTime, nullable=False)
    end_datetime = db.Column(db.DateTime)
    event_type = db.Column(db.String(50))  # reminder, deadline, exam, meeting, study_session
    importance = db.Column(db.Integer, default=1)  # 1-5 scale
    is_completed = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'content_id': self.content_id,
            'title': self.title,
            'description': self.description,
            'start_datetime': self.start_datetime.isoformat(),
            'end_datetime': self.end_datetime.isoformat() if self.end_datetime else None,
            'event_type': self.event_type,
            'importance': self.importance,
            'is_completed': self.is_completed,
            'created_at': self.created_at.isoformat()
        }

class SearchHistory(db.Model):
    """Search history for analytics and improvements"""
    __tablename__ = 'search_history'
    
    id = db.Column(db.Integer, primary_key=True)
    query = db.Column(db.Text, nullable=False)
    results_count = db.Column(db.Integer)
    filters = db.Column(db.Text)  # JSON string
    execution_time = db.Column(db.Float)  # Search execution time in seconds
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def get_filters(self):
        """Get filters as dictionary"""
        if self.filters:
            try:
                return json.loads(self.filters)
            except json.JSONDecodeError:
                return {}
        return {}
    
    def set_filters(self, filters_dict):
        """Set filters from dictionary"""
        self.filters = json.dumps(filters_dict)
    
    def to_dict(self):
        return {
            'id': self.id,
            'query': self.query,
            'results_count': self.results_count,
            'filters': self.get_filters(),
            'execution_time': self.execution_time,
            'created_at': self.created_at.isoformat()
        }
