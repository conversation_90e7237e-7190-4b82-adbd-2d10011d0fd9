/**
 * Verdure Animations - Advanced animation system
 */

// Animation state management
window.VerdureAnimations = {
    initialized: false,
    activeAnimations: new Map(),
    animationQueue: [],
    performance: {
        preferReducedMotion: false,
        disableAnimations: false
    }
};

/**
 * Initialize animation system
 */
function initializeAnimations() {
    if (window.VerdureAnimations.initialized) return;
    
    // Check for reduced motion preference
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)');
    window.VerdureAnimations.performance.preferReducedMotion = prefersReducedMotion.matches;
    
    // Listen for changes in motion preference
    prefersReducedMotion.addEventListener('change', (e) => {
        window.VerdureAnimations.performance.preferReducedMotion = e.matches;
    });
    
    // Initialize intersection observer for scroll animations
    initializeScrollAnimations();
    
    // Start ambient animations
    startAmbientAnimations();
    
    window.VerdureAnimations.initialized = true;
    console.log('🎨 Animation system initialized');
}

/**
 * Animate element with specified animation
 */
function animateElement(element, animationName, options = {}) {
    if (!element || window.VerdureAnimations.performance.disableAnimations) return;
    
    const config = {
        duration: 300,
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
        delay: 0,
        ...options
    };
    
    // Remove any existing animations
    element.classList.remove(...getAnimationClasses());
    
    // Apply animation class
    element.classList.add(animationName);
    
    // Track animation
    const animationId = Math.random().toString(36).substr(2, 9);
    window.VerdureAnimations.activeAnimations.set(animationId, {
        element,
        animationName,
        startTime: Date.now()
    });
    
    // Clean up after animation completes
    setTimeout(() => {
        element.classList.remove(animationName);
        window.VerdureAnimations.activeAnimations.delete(animationId);
    }, config.duration + config.delay);
    
    return animationId;
}

/**
 * Get all animation class names
 */
function getAnimationClasses() {
    return [
        'fade-in', 'fade-out', 'slide-in-left', 'slide-in-right', 
        'slide-in-up', 'slide-in-down', 'scale-in', 'scale-out',
        'bounce-in', 'pulse', 'shake', 'float', 'glow',
        'stagger-item', 'processing-dots', 'success-checkmark'
    ];
}

/**
 * Show welcome animation
 */
function showWelcomeAnimation() {
    if (window.VerdureAnimations.performance.preferReducedMotion) return;
    
    const logo = document.querySelector('.logo-container');
    const navItems = document.querySelectorAll('.nav-item');
    const mainContent = document.querySelector('.main-content');
    
    // Animate logo
    if (logo) {
        animateElement(logo, 'bounce-in', { delay: 100 });
    }
    
    // Stagger animate nav items
    navItems.forEach((item, index) => {
        setTimeout(() => {
            animateElement(item, 'slide-in-left', { delay: index * 50 });
        }, 200 + index * 100);
    });
    
    // Animate main content
    if (mainContent) {
        animateElement(mainContent, 'fade-in', { delay: 500 });
    }
}

/**
 * Initialize scroll-triggered animations
 */
function initializeScrollAnimations() {
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                const element = entry.target;
                const animationType = element.dataset.animation || 'fade-in';
                animateElement(element, animationType);
                observer.unobserve(element);
            }
        });
    }, observerOptions);
    
    // Observe elements with animation triggers
    const animatedElements = document.querySelectorAll('[data-animation]');
    animatedElements.forEach(el => observer.observe(el));
}

/**
 * Start ambient animations (subtle background effects)
 */
function startAmbientAnimations() {
    if (window.VerdureAnimations.performance.preferReducedMotion) return;
    
    // Floating animation for logo
    const logo = document.querySelector('.logo-icon');
    if (logo) {
        logo.classList.add('float');
    }
    
    // Pulse animation for AI status
    const aiStatus = document.querySelector('.ai-status .status-indicator');
    if (aiStatus) {
        aiStatus.classList.add('pulse');
    }
    
    // Glow effect for active nav items
    const activeNavItem = document.querySelector('.nav-item.active');
    if (activeNavItem) {
        activeNavItem.classList.add('glow');
    }
}

/**
 * Processing animation controller
 */
class ProcessingAnimation {
    constructor(container, message = 'Processing...') {
        this.container = container;
        this.message = message;
        this.isActive = false;
    }
    
    start() {
        if (this.isActive) return;
        
        this.isActive = true;
        this.container.innerHTML = `
            <div class="processing-content">
                <div class="processing-spinner"></div>
                <div class="processing-text">
                    <span class="processing-dots">${this.message}</span>
                </div>
            </div>
        `;
        
        animateElement(this.container, 'fade-in');
        this.container.classList.add('processing-active');
    }
    
    stop() {
        if (!this.isActive) return;
        
        this.isActive = false;
        animateElement(this.container, 'fade-out');
        
        setTimeout(() => {
            this.container.classList.remove('processing-active');
            this.container.innerHTML = '';
        }, 300);
    }
    
    updateMessage(message) {
        if (!this.isActive) return;
        
        const textElement = this.container.querySelector('.processing-dots');
        if (textElement) {
            textElement.textContent = message;
        }
    }
}

/**
 * Stagger animation for lists
 */
function animateStaggeredList(container, delay = 100) {
    const items = container.querySelectorAll('.stagger-item');
    
    items.forEach((item, index) => {
        setTimeout(() => {
            animateElement(item, 'slide-in-up', { delay: index * 50 });
        }, index * delay);
    });
}

/**
 * Success animation
 */
function showSuccessAnimation(element, callback) {
    // Add success visual feedback
    const successIcon = document.createElement('div');
    successIcon.className = 'success-indicator';
    successIcon.innerHTML = '<i class="fas fa-check"></i>';
    
    element.appendChild(successIcon);
    animateElement(successIcon, 'success-checkmark');
    
    setTimeout(() => {
        element.removeChild(successIcon);
        if (callback) callback();
    }, 1500);
}

/**
 * Error shake animation
 */
function showErrorAnimation(element) {
    animateElement(element, 'shake');
    element.classList.add('error-state');
    
    setTimeout(() => {
        element.classList.remove('error-state');
    }, 2000);
}

/**
 * File upload animation
 */
function animateFileUpload(fileElement, progress = 0) {
    const progressBar = fileElement.querySelector('.upload-progress-bar');
    if (progressBar) {
        progressBar.style.width = `${progress}%`;
        
        if (progress === 100) {
            setTimeout(() => {
                showSuccessAnimation(fileElement);
            }, 500);
        }
    }
}

/**
 * Card hover animations
 */
function initializeCardAnimations() {
    const cards = document.querySelectorAll('.card, .course-card, .content-card');
    
    cards.forEach(card => {
        card.addEventListener('mouseenter', () => {
            if (!window.VerdureAnimations.performance.preferReducedMotion) {
                card.style.transform = 'translateY(-5px) rotateX(2deg)';
                card.style.boxShadow = '0 15px 35px rgba(0,0,0,0.1)';
            }
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = '';
            card.style.boxShadow = '';
        });
    });
}

/**
 * Loading bar animation
 */
function showLoadingBar() {
    const loadingBar = document.createElement('div');
    loadingBar.className = 'global-loading-bar';
    loadingBar.innerHTML = '<div class="loading-progress"></div>';
    
    document.body.appendChild(loadingBar);
    animateElement(loadingBar, 'slide-in-down');
    
    return {
        update: (progress) => {
            const progressEl = loadingBar.querySelector('.loading-progress');
            if (progressEl) {
                progressEl.style.width = `${progress}%`;
            }
        },
        complete: () => {
            animateElement(loadingBar, 'fade-out');
            setTimeout(() => {
                if (loadingBar.parentNode) {
                    loadingBar.parentNode.removeChild(loadingBar);
                }
            }, 300);
        }
    };
}

/**
 * Initialize all animation features
 */
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(initializeAnimations, 100);
    setTimeout(initializeCardAnimations, 200);
});
