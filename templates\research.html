{% extends "base.html" %}

{% block title %}Research - Verdure{% endblock %}

{% block extra_css %}
<style>
    .research-container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 2rem;
    }

    .research-hero {
        text-align: center;
        padding: 4rem 0;
        background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
        color: white;
        border-radius: 20px;
        margin-bottom: 3rem;
        position: relative;
        overflow: hidden;
    }

    .research-hero::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="2" fill="white" opacity="0.1"/><circle cx="80" cy="40" r="1" fill="white" opacity="0.1"/><circle cx="40" cy="80" r="1.5" fill="white" opacity="0.1"/><circle cx="70" cy="70" r="1" fill="white" opacity="0.1"/></svg>');
        animation: float 20s infinite linear;
    }

    .research-hero h1 {
        font-size: 3rem;
        margin-bottom: 1rem;
        font-weight: 700;
    }

    .research-hero p {
        font-size: 1.2rem;
        opacity: 0.9;
        max-width: 600px;
        margin: 0 auto;
    }

    .coming-soon-badge {
        display: inline-block;
        background: rgba(255, 255, 255, 0.2);
        padding: 0.5rem 1rem;
        border-radius: 25px;
        margin-top: 1rem;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.3);
    }

    .features-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 2rem;
        margin-bottom: 3rem;
    }

    .feature-card {
        background: white;
        padding: 2rem;
        border-radius: 15px;
        box-shadow: var(--shadow-light);
        transition: var(--transition-smooth);
        position: relative;
        overflow: hidden;
    }

    .feature-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
    }

    .feature-card:hover {
        transform: translateY(-5px);
        box-shadow: var(--shadow-heavy);
    }

    .feature-icon {
        font-size: 3rem;
        margin-bottom: 1rem;
        display: block;
    }

    .feature-title {
        font-size: 1.5rem;
        font-weight: 600;
        margin-bottom: 1rem;
        color: var(--text-primary);
    }

    .feature-description {
        color: #666;
        line-height: 1.6;
        margin-bottom: 1.5rem;
    }

    .feature-status {
        display: inline-block;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.875rem;
        font-weight: 500;
    }

    .status-planned {
        background: #e3f2fd;
        color: #1976d2;
    }

    .status-development {
        background: #fff3e0;
        color: #f57c00;
    }

    .roadmap-section {
        background: #f8f9fa;
        padding: 3rem;
        border-radius: 20px;
        margin-bottom: 3rem;
    }

    .roadmap-title {
        text-align: center;
        font-size: 2rem;
        margin-bottom: 2rem;
        color: var(--text-primary);
    }

    .roadmap-timeline {
        position: relative;
        max-width: 800px;
        margin: 0 auto;
    }

    .roadmap-timeline::before {
        content: '';
        position: absolute;
        left: 50%;
        top: 0;
        bottom: 0;
        width: 2px;
        background: var(--primary-color);
        transform: translateX(-50%);
    }

    .roadmap-item {
        position: relative;
        margin-bottom: 2rem;
        width: 45%;
    }

    .roadmap-item:nth-child(odd) {
        left: 0;
        text-align: right;
        padding-right: 2rem;
    }

    .roadmap-item:nth-child(even) {
        left: 55%;
        text-align: left;
        padding-left: 2rem;
    }

    .roadmap-item::before {
        content: '';
        position: absolute;
        width: 12px;
        height: 12px;
        background: var(--primary-color);
        border-radius: 50%;
        top: 0;
    }

    .roadmap-item:nth-child(odd)::before {
        right: -6px;
    }

    .roadmap-item:nth-child(even)::before {
        left: -6px;
    }

    .roadmap-content {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: var(--shadow-light);
    }

    .roadmap-date {
        font-weight: 600;
        color: var(--primary-color);
        margin-bottom: 0.5rem;
    }

    .roadmap-feature {
        font-size: 1.1rem;
        font-weight: 500;
        margin-bottom: 0.5rem;
    }

    .early-access-section {
        text-align: center;
        padding: 3rem;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 20px;
    }

    .early-access-title {
        font-size: 2rem;
        margin-bottom: 1rem;
    }

    .early-access-button {
        display: inline-block;
        background: white;
        color: #667eea;
        padding: 1rem 2rem;
        border-radius: 50px;
        text-decoration: none;
        font-weight: 600;
        margin-top: 1rem;
        transition: var(--transition-smooth);
    }

    .early-access-button:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        color: #667eea;
        text-decoration: none;
    }

    @keyframes float {
        0% { transform: translateX(-100%); }
        100% { transform: translateX(100%); }
    }

    @media (max-width: 768px) {
        .research-hero h1 {
            font-size: 2rem;
        }
        
        .roadmap-timeline::before {
            left: 20px;
        }
        
        .roadmap-item {
            width: calc(100% - 40px);
            left: 40px !important;
            text-align: left !important;
            padding-left: 1rem !important;
            padding-right: 0 !important;
        }
        
        .roadmap-item::before {
            left: -26px !important;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="research-container animate-on-scroll">
    <!-- Hero Section -->
    <div class="research-hero">
        <h1>🔬 Research Hub</h1>
        <p>Advanced AI-powered research capabilities to accelerate your academic journey</p>
        <div class="coming-soon-badge">
            Coming Soon in v2.0
        </div>
    </div>

    <!-- Planned Features -->
    <div class="features-grid">
        <div class="feature-card animate-on-scroll">
            <div class="feature-icon">🎓</div>
            <div class="feature-title">Academic Search</div>
            <div class="feature-description">
                Advanced search across academic databases including Google Scholar, PubMed, arXiv, and IEEE Xplore. Get intelligent recommendations based on your research interests.
            </div>
            <span class="feature-status status-planned">Planned</span>
        </div>

        <div class="feature-card animate-on-scroll">
            <div class="feature-icon">🤖</div>
            <div class="feature-title">AI Research Assistant</div>
            <div class="feature-description">
                Intelligent agent that can understand your research goals, suggest relevant papers, summarize key findings, and identify research gaps in your field.
            </div>
            <span class="feature-status status-development">In Development</span>
        </div>

        <div class="feature-card animate-on-scroll">
            <div class="feature-icon">📚</div>
            <div class="feature-title">Citation Management</div>
            <div class="feature-description">
                Automated citation extraction and formatting in APA, MLA, Chicago, and other styles. Smart duplicate detection and bibliography generation.
            </div>
            <span class="feature-status status-planned">Planned</span>
        </div>

        <div class="feature-card animate-on-scroll">
            <div class="feature-icon">📊</div>
            <div class="feature-title">Research Analytics</div>
            <div class="feature-description">
                Visualize research trends, track citation networks, analyze collaboration patterns, and discover emerging topics in your field.
            </div>
            <span class="feature-status status-planned">Planned</span>
        </div>

        <div class="feature-card animate-on-scroll">
            <div class="feature-icon">🧠</div>
            <div class="feature-title">Knowledge Synthesis</div>
            <div class="feature-description">
                AI-powered synthesis of multiple research papers into coherent summaries, identifying contradictions and consensus across studies.
            </div>
            <span class="feature-status status-planned">Planned</span>
        </div>

        <div class="feature-card animate-on-scroll">
            <div class="feature-icon">🔗</div>
            <div class="feature-title">Collaboration Tools</div>
            <div class="feature-description">
                Share research findings with colleagues, collaborate on literature reviews, and maintain synchronized research libraries across teams.
            </div>
            <span class="feature-status status-planned">Planned</span>
        </div>
    </div>

    <!-- Development Roadmap -->
    <div class="roadmap-section animate-on-scroll">
        <h2 class="roadmap-title">Development Roadmap</h2>
        <div class="roadmap-timeline">
            <div class="roadmap-item">
                <div class="roadmap-content">
                    <div class="roadmap-date">Q3 2025</div>
                    <div class="roadmap-feature">Basic Search Interface</div>
                    <p>Integration with Google Scholar API and basic search functionality</p>
                </div>
            </div>
            <div class="roadmap-item">
                <div class="roadmap-content">
                    <div class="roadmap-date">Q4 2025</div>
                    <div class="roadmap-feature">AI Research Assistant</div>
                    <p>Intelligent research agent with paper summarization and recommendation capabilities</p>
                </div>
            </div>
            <div class="roadmap-item">
                <div class="roadmap-content">
                    <div class="roadmap-date">Q1 2026</div>
                    <div class="roadmap-feature">Citation Management</div>
                    <p>Automated citation extraction, formatting, and bibliography generation</p>
                </div>
            </div>
            <div class="roadmap-item">
                <div class="roadmap-content">
                    <div class="roadmap-date">Q2 2026</div>
                    <div class="roadmap-feature">Advanced Analytics</div>
                    <p>Research trend analysis, citation networks, and collaboration insights</p>
                </div>
            </div>
            <div class="roadmap-item">
                <div class="roadmap-content">
                    <div class="roadmap-date">Q3 2026</div>
                    <div class="roadmap-feature">Knowledge Synthesis</div>
                    <p>AI-powered synthesis and analysis of multiple research papers</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Early Access -->
    <div class="early-access-section animate-on-scroll">
        <h2 class="early-access-title">Get Early Access</h2>
        <p>Be among the first to experience the future of academic research. Join our beta program and help shape the development of these powerful research tools.</p>
        <a href="#" class="early-access-button" onclick="showNotification('Early access registration coming soon!', 'info')">
            Request Early Access
        </a>
    </div>
</div>

<script>
// Add some interactive elements
document.addEventListener('DOMContentLoaded', function() {
    // Animate feature cards on scroll
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    });

    document.querySelectorAll('.feature-card').forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(30px)';
        card.style.transition = `opacity 0.6s ease ${index * 0.1}s, transform 0.6s ease ${index * 0.1}s`;
        observer.observe(card);
    });

    // Add hover effects to roadmap items
    document.querySelectorAll('.roadmap-item').forEach(item => {
        item.addEventListener('mouseenter', function() {
            this.querySelector('.roadmap-content').style.transform = 'scale(1.05)';
        });
        
        item.addEventListener('mouseleave', function() {
            this.querySelector('.roadmap-content').style.transform = 'scale(1)';
        });
    });
});
</script>
{% endblock %}
