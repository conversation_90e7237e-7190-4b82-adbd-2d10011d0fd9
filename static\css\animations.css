/* Verdure Animations - Bringing the Interface to Life */

/* Keyframe Animations */

/* Gentle floating animation for logo */
@keyframes gentle-float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-6px);
    }
}

/* Pulsing dot for status indicators */
@keyframes pulse-dot {
    0%, 100% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.2);
    }
}

/* Glowing pulse effect for active inputs */
@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(46, 204, 113, 0.3), 0 4px 20px rgba(0, 0, 0, 0.15);
    }
    50% {
        box-shadow: 0 0 20px rgba(46, 204, 113, 0.6), 0 8px 30px rgba(0, 0, 0, 0.25);
    }
}

/* Sliding animations */
@keyframes slide-down {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slide-up {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slide-in-left {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slide-in-right {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* Fade animations */
@keyframes fade-in {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes fade-in-scale {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Processing spinner animation */
@keyframes processing-spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Loading bar animation */
@keyframes loading-slide {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(100%);
    }
}

/* Typing dots animation */
@keyframes typing-dots {
    0% {
        content: "Processing";
    }
    25% {
        content: "Processing.";
    }
    50% {
        content: "Processing..";
    }
    75% {
        content: "Processing...";
    }
    100% {
        content: "Processing";
    }
}

/* Bounce entrance animation */
@keyframes bounce-in {
    0% {
        opacity: 0;
        transform: scale(0.3) translateY(-50px);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* Shake animation for errors */
@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

/* Success checkmark animation */
@keyframes success-checkmark {
    0% {
        stroke-dasharray: 0 50;
        opacity: 0;
    }
    50% {
        opacity: 1;
    }
    100% {
        stroke-dasharray: 50 0;
        opacity: 1;
    }
}

/* Progress bar fill animation */
@keyframes progress-fill {
    0% {
        width: 0%;
    }
    100% {
        width: var(--progress-width, 100%);
    }
}

/* Morphing icon animation */
@keyframes morph-icon {
    0%, 100% {
        transform: rotate(0deg) scale(1);
    }
    50% {
        transform: rotate(180deg) scale(1.2);
    }
}

/* Ripple effect animation */
@keyframes ripple {
    0% {
        transform: scale(0);
        opacity: 1;
    }
    100% {
        transform: scale(4);
        opacity: 0;
    }
}

/* Breathing animation for background elements */
@keyframes breathing {
    0%, 100% {
        transform: scale(1);
        opacity: 0.1;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.2;
    }
}

/* Stagger animation for list items */
@keyframes stagger-in {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Animation Classes */

/* Basic animations */
.animate-fade-in {
    animation: fade-in 0.3s ease-out;
}

.animate-fade-in-scale {
    animation: fade-in-scale 0.4s ease-out;
}

.animate-slide-down {
    animation: slide-down 0.3s ease-out;
}

.animate-slide-up {
    animation: slide-up 0.3s ease-out;
}

.animate-slide-in-left {
    animation: slide-in-left 0.4s ease-out;
}

.animate-slide-in-right {
    animation: slide-in-right 0.4s ease-out;
}

.animate-bounce-in {
    animation: bounce-in 0.6s ease-out;
}

.animate-shake {
    animation: shake 0.6s ease-in-out;
}

/* Processing animations */
.processing-indicator {
    position: relative;
}

.processing-indicator::after {
    content: "Processing";
    animation: typing-dots 1.5s infinite;
}

.processing-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--light-gray);
    border-top: 4px solid var(--verdure-green);
    border-radius: 50%;
    animation: processing-spin 1s linear infinite;
    margin: 0 auto;
}

.processing-spinner.large {
    width: 60px;
    height: 60px;
    border-width: 6px;
}

.processing-spinner.small {
    width: 20px;
    height: 20px;
    border-width: 2px;
}

/* Loading bars */
.loading-bar {
    width: 100%;
    height: 4px;
    background: var(--light-gray);
    border-radius: var(--radius-full);
    overflow: hidden;
    position: relative;
}

.loading-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 40%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    animation: loading-slide 2s infinite;
}

.loading-bar.fast::before {
    animation-duration: 1s;
}

.loading-bar.slow::before {
    animation-duration: 3s;
}

/* Progress bars */
.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--light-gray);
    border-radius: var(--radius-full);
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: var(--radius-full);
    transition: width 0.3s ease;
    animation: progress-fill 1s ease-out;
}

/* Pulse animations */
.pulse-glow {
    animation: pulse-glow 2s infinite;
}

.pulse-dot {
    animation: pulse-dot 2s infinite;
}

/* Hover effects */
.hover-lift {
    transition: var(--transition-smooth);
}

.hover-lift:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-heavy);
}

.hover-scale {
    transition: var(--transition-smooth);
}

.hover-scale:hover {
    transform: scale(1.05);
}

.hover-glow {
    transition: var(--transition-smooth);
}

.hover-glow:hover {
    box-shadow: var(--shadow-glow);
}

/* Ripple effect */
.ripple-effect {
    position: relative;
    overflow: hidden;
}

.ripple-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::before {
    width: 300px;
    height: 300px;
}

/* Stagger animations for lists */
.stagger-container .stagger-item {
    opacity: 0;
    animation: stagger-in 0.4s ease-out forwards;
}

.stagger-container .stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-container .stagger-item:nth-child(2) { animation-delay: 0.2s; }
.stagger-container .stagger-item:nth-child(3) { animation-delay: 0.3s; }
.stagger-container .stagger-item:nth-child(4) { animation-delay: 0.4s; }
.stagger-container .stagger-item:nth-child(5) { animation-delay: 0.5s; }
.stagger-container .stagger-item:nth-child(6) { animation-delay: 0.6s; }

/* Breathing background elements */
.breathing-bg {
    animation: breathing 4s ease-in-out infinite;
}

/* Icon morphing */
.morph-icon {
    transition: var(--transition-smooth);
}

.morph-icon.active {
    animation: morph-icon 0.6s ease-in-out;
}

/* AI processing states */
.ai-processing {
    position: relative;
}

.ai-processing::after {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--verdure-green), var(--deep-blue), var(--verdure-green));
    background-size: 400% 400%;
    border-radius: inherit;
    z-index: -1;
    animation: gradient-flow 2s ease infinite;
}

@keyframes gradient-flow {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

/* Success states */
.success-state {
    color: var(--verdure-green);
    animation: fade-in-scale 0.4s ease-out;
}

.success-checkmark {
    width: 24px;
    height: 24px;
    stroke: var(--verdure-green);
    stroke-width: 2;
    fill: none;
    animation: success-checkmark 0.6s ease-out;
}

/* Error states */
.error-state {
    color: var(--soft-red);
    animation: shake 0.6s ease-in-out;
}

/* Loading states */
.loading-skeleton {
    background: linear-gradient(90deg, var(--light-gray) 25%, var(--medium-gray) 50%, var(--light-gray) 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
    0% {
        background-position: -200% 0;
    }
    100% {
        background-position: 200% 0;
    }
}

/* Overlay animations */
.overlay-enter {
    animation: fade-in 0.3s ease-out;
}

.overlay-exit {
    animation: fade-out 0.3s ease-out;
}

@keyframes fade-out {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* Modal animations */
.modal-enter {
    animation: modal-slide-up 0.4s ease-out;
}

.modal-exit {
    animation: modal-slide-down 0.3s ease-in;
}

@keyframes modal-slide-up {
    from {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes modal-slide-down {
    from {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    to {
        opacity: 0;
        transform: translateY(50px) scale(0.9);
    }
}

/* Notification animations */
.notification-enter {
    animation: notification-slide-in 0.4s ease-out;
}

.notification-exit {
    animation: notification-slide-out 0.3s ease-in;
}

@keyframes notification-slide-in {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes notification-slide-out {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(100%);
    }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}
