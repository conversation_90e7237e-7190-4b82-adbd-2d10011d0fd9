"""
Verdure - AI-Powered Student Assistant
Main Flask application entry point
"""

from flask import Flask, render_template, request, jsonify, session
from flask_sqlalchemy import SQLAlchemy
from werkzeug.utils import secure_filename
import os
from datetime import datetime
import asyncio
import json

# Import our custom modules
from config import Config
from models.database import db, Content, Course, CalendarEvent
from services.openrouter_client import OpenRouterClient
from services.categorizer import InputCategorizer
from services.file_processor import FileProcessor
from services.calendar_service import CalendarService
from models.embeddings import EmbeddingService

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # Initialize database
    db.init_app(app)
    
    # Initialize services
    app.openrouter_client = OpenRouterClient()
    app.categorizer = InputCategorizer(app.openrouter_client)
    app.file_processor = FileProcessor()
    app.calendar_service = CalendarService()
    app.embedding_service = EmbeddingService()
    
    # Create tables
    with app.app_context():
        db.create_all()
    
    return app

app = create_app()

# Routes
@app.route('/')
def dashboard():
    """Main dashboard page"""
    courses = Course.query.all()
    recent_content = Content.query.order_by(Content.created_at.desc()).limit(5).all()
    upcoming_events = CalendarEvent.query.filter(
        CalendarEvent.start_datetime > datetime.now()
    ).order_by(CalendarEvent.start_datetime).limit(3).all()
    
    return render_template('dashboard.html', 
                         courses=courses,
                         recent_content=recent_content,
                         upcoming_events=upcoming_events)

@app.route('/courses')
def courses():
    """Courses page"""
    all_courses = Course.query.all()
    return render_template('courses.html', courses=all_courses)

@app.route('/api/process_input', methods=['POST'])
async def process_input():
    """Process user input with AI categorization"""
    try:
        data = request.get_json()
        user_input = data.get('input', '')
        files = data.get('files', [])
        
        # Use AI to categorize the input
        category_result = await app.categorizer.categorize_input(user_input, files)
        
        # Process based on category
        processed_content = await app.categorizer.process_content(
            user_input, category_result['category'], files
        )
        
        # Generate embeddings for semantic search
        if user_input:
            await app.embedding_service.index_content(
                processed_content['id'], 
                user_input, 
                processed_content['metadata']
            )
        
        return jsonify({
            'success': True,
            'category': category_result['category'],
            'confidence': category_result['confidence'],
            'processed_content': processed_content,
            'message': f"Successfully categorized as {category_result['category']}"
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/semantic_search', methods=['POST'])
async def semantic_search():
    """Perform semantic search across indexed content"""
    try:
        data = request.get_json()
        query = data.get('query', '')
        filters = data.get('filters', {})
        use_ai_agent = data.get('use_ai_agent', False)
        
        if use_ai_agent:
            # Use AI agent for intelligent search
            results = await app.embedding_service.ai_search_agent(query, filters)
        else:
            # Regular semantic search
            results = await app.embedding_service.search(query, filters)
        
        return jsonify({
            'success': True,
            'results': results,
            'query': query
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/upload_file', methods=['POST'])
async def upload_file():
    """Handle file uploads with AI processing"""
    try:
        if 'file' not in request.files:
            return jsonify({'success': False, 'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'success': False, 'error': 'No file selected'}), 400
        
        # Secure filename and save
        filename = secure_filename(file.filename)
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Process file with AI
        processed_data = await app.file_processor.process_file(filepath)
        
        # Categorize the file content
        category_result = await app.categorizer.categorize_input(
            processed_data['text'], 
            [{'filename': filename, 'type': processed_data['type']}]
        )
        
        return jsonify({
            'success': True,
            'filename': filename,
            'category': category_result['category'],
            'processed_data': processed_data
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/create_course', methods=['POST'])
def create_course():
    """Create a new course"""
    try:
        data = request.get_json()
        course = Course(
            name=data['name'],
            code=data.get('code', ''),
            color=data.get('color', '#3498db'),
            description=data.get('description', '')
        )
        db.session.add(course)
        db.session.commit()
        
        return jsonify({
            'success': True,
            'course_id': course.id,
            'message': 'Course created successfully'
        })
        
    except Exception as e:
        db.session.rollback()
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/generate_study_guide', methods=['POST'])
async def generate_study_guide():
    """Generate AI-powered study guide for a course"""
    try:
        data = request.get_json()
        course_id = data.get('course_id')
        
        # Get all content for the course
        course_content = Content.query.filter_by(course_id=course_id).all()
        
        # Use AI to generate comprehensive study guide
        study_guide = await app.categorizer.generate_study_guide(course_content)
        
        return jsonify({
            'success': True,
            'study_guide': study_guide
        })
        
    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@app.route('/api/calendar_events')
def get_calendar_events():
    """Get calendar events for the calendar widget"""
    events = CalendarEvent.query.all()
    events_data = []
    
    for event in events:
        events_data.append({
            'id': event.id,
            'title': event.title,
            'start': event.start_datetime.isoformat(),
            'end': event.end_datetime.isoformat() if event.end_datetime else None,
            'type': event.event_type,
            'importance': event.importance
        })
    
    return jsonify(events_data)

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)
