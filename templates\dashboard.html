{% extends "base.html" %}

{% block title %}Dashboard - Verdure{% endblock %}
{% block page_title %}Dashboard{% endblock %}

{% block content %}
<div class="dashboard-layout">
    <!-- Smart Input Section -->
    <section class="smart-input-section">
        <div class="input-container">
            <div class="input-wrapper pulse-input" id="smartInputWrapper">
                <div class="input-area">
                    <textarea class="smart-input" 
                              id="smartInput" 
                              placeholder="What would you like to do? Type anything or drag files here..."
                              rows="3"></textarea>
                    
                    <div class="input-actions">
                        <button class="file-upload-btn" onclick="triggerFileUpload()">
                            <i class="fas fa-paperclip"></i>
                            <span>Attach</span>
                        </button>
                        
                        <button class="voice-input-btn" onclick="toggleVoiceInput()" title="Voice Input">
                            <i class="fas fa-microphone"></i>
                        </button>
                        
                        <button class="submit-btn" onclick="processInput()" id="submitBtn">
                            <i class="fas fa-paper-plane"></i>
                            <span>Process</span>
                        </button>
                    </div>
                </div>
                
                <!-- File Upload Area -->
                <div class="file-drop-zone" id="fileDropZone">
                    <i class="fas fa-cloud-upload-alt"></i>
                    <p>Drop files here or click to browse</p>
                    <input type="file" id="fileInput" multiple hidden>
                </div>
                
                <!-- Categorization Display -->
                <div class="categorization-display" id="categorizationDisplay">
                    <div class="category-indicator">
                        <span class="category-label" id="categoryLabel"></span>
                        <span class="confidence-score" id="confidenceScore"></span>
                    </div>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Dashboard Grid -->
    <div class="dashboard-grid">
        <!-- Calendar Widget -->
        <section class="dashboard-card calendar-card">
            <div class="card-header">
                <h3><i class="fas fa-calendar-alt"></i> Calendar</h3>
                <div class="card-actions">
                    <button class="view-all-btn" onclick="expandCalendar()">
                        <i class="fas fa-expand"></i>
                    </button>
                </div>
            </div>
            
            <div class="card-content">
                <div class="mini-calendar" id="miniCalendar">
                    <!-- Calendar will be populated by JavaScript -->
                </div>
                
                <div class="upcoming-events">
                    <h4>Upcoming Events</h4>
                    <div class="events-list" id="upcomingEventsList">
                        {% for event in upcoming_events %}
                        <div class="event-item {{ event.event_type }}">
                            <div class="event-time">
                                {{ event.start_datetime.strftime('%m/%d %H:%M') }}
                            </div>
                            <div class="event-details">
                                <span class="event-title">{{ event.title }}</span>
                                <span class="event-type">{{ event.event_type }}</span>
                            </div>
                            <div class="importance-indicator importance-{{ event.importance }}"></div>
                        </div>
                        {% endfor %}
                    </div>
                </div>
            </div>
        </section>
        
        <!-- Quick Courses Access -->
        <section class="dashboard-card courses-card">
            <div class="card-header">
                <h3><i class="fas fa-book"></i> Quick Course Access</h3>
                <div class="card-actions">
                    <button class="add-btn" onclick="showCreateCourseModal()">
                        <i class="fas fa-plus"></i>
                    </button>
                </div>
            </div>
            
            <div class="card-content">
                <div class="courses-grid">
                    {% for course in courses %}
                    <div class="course-quick-card" 
                         style="border-left: 4px solid {{ course.color }}"
                         onclick="openCourse({{ course.id }})">
                        <div class="course-info">
                            <h4>{{ course.name }}</h4>
                            {% if course.code %}
                            <span class="course-code">{{ course.code }}</span>
                            {% endif %}
                        </div>
                        <div class="course-stats">
                            <span class="content-count">{{ course.content|length }} items</span>
                        </div>
                    </div>
                    {% endfor %}
                    
                    {% if not courses %}
                    <div class="empty-state">
                        <i class="fas fa-plus-circle"></i>
                        <p>Create your first course to get started</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </section>
        
        <!-- Recent Activity -->
        <section class="dashboard-card activity-card">
            <div class="card-header">
                <h3><i class="fas fa-clock"></i> Recent Activity</h3>
            </div>
            
            <div class="card-content">
                <div class="activity-timeline">
                    {% for content in recent_content %}
                    <div class="activity-item">
                        <div class="activity-icon {{ content.content_type }}">
                            {% if content.content_type == 'task' %}
                            <i class="fas fa-tasks"></i>
                            {% elif content.content_type == 'reminder' %}
                            <i class="fas fa-bell"></i>
                            {% elif content.content_type == 'event' %}
                            <i class="fas fa-calendar"></i>
                            {% elif content.content_type == 'lecture_slides' %}
                            <i class="fas fa-presentation"></i>
                            {% else %}
                            <i class="fas fa-file"></i>
                            {% endif %}
                        </div>
                        
                        <div class="activity-details">
                            <span class="activity-title">{{ content.title }}</span>
                            <span class="activity-time">{{ content.created_at.strftime('%m/%d %H:%M') }}</span>
                            <span class="activity-type">{{ content.content_type.title() }}</span>
                        </div>
                    </div>
                    {% endfor %}
                    
                    {% if not recent_content %}
                    <div class="empty-state">
                        <i class="fas fa-inbox"></i>
                        <p>No recent activity</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </section>
        
        <!-- Important Dates -->
        <section class="dashboard-card important-dates-card">
            <div class="card-header">
                <h3><i class="fas fa-exclamation-triangle"></i> Important Dates</h3>
            </div>
            
            <div class="card-content">
                <div class="important-dates-list" id="importantDatesList">
                    <!-- Will be populated by JavaScript -->
                </div>
            </div>
        </section>
        
        <!-- Quick Search -->
        <section class="dashboard-card search-card">
            <div class="card-header">
                <h3><i class="fas fa-search"></i> Quick Search</h3>
                <div class="card-actions">
                    <button class="ai-toggle-btn" id="quickSearchAIToggle" onclick="toggleQuickSearchAI()">
                        <i class="fas fa-brain"></i>
                    </button>
                </div>
            </div>
            
            <div class="card-content">
                <div class="search-input-container">
                    <input type="text" 
                           class="quick-search-input" 
                           id="quickSearchInput"
                           placeholder="Search your content..."
                           onkeypress="handleQuickSearchKeypress(event)">
                    <button class="search-btn" onclick="performQuickSearch()">
                        <i class="fas fa-search"></i>
                    </button>
                </div>
                
                <div class="quick-search-results" id="quickSearchResults">
                    <!-- Search results will appear here -->
                </div>
            </div>
        </section>
        
        <!-- AI Processing Status -->
        <section class="dashboard-card ai-status-card">
            <div class="card-header">
                <h3><i class="fas fa-brain"></i> AI Status</h3>
            </div>
            
            <div class="card-content">
                <div class="ai-metrics">
                    <div class="metric">
                        <span class="metric-label">Items Indexed</span>
                        <span class="metric-value" id="indexedItemsCount">0</span>
                    </div>
                    
                    <div class="metric">
                        <span class="metric-label">Processing Queue</span>
                        <span class="metric-value" id="processingQueueCount">0</span>
                    </div>
                    
                    <div class="metric">
                        <span class="metric-label">Models Status</span>
                        <div class="models-status">
                            <span class="model-indicator" id="deepseekStatus">
                                <i class="fas fa-circle"></i> DeepSeek
                            </span>
                            <span class="model-indicator" id="geminiStatus">
                                <i class="fas fa-circle"></i> Gemini
                            </span>
                            <span class="model-indicator" id="ollamaStatus">
                                <i class="fas fa-circle"></i> Ollama
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </div>
</div>

<!-- Create Course Modal -->
<div class="modal" id="createCourseModal">
    <div class="modal-content">
        <div class="modal-header">
            <h3>Create New Course</h3>
            <button class="close-btn" onclick="closeModal('createCourseModal')">
                <i class="fas fa-times"></i>
            </button>
        </div>
        
        <div class="modal-body">
            <form id="createCourseForm">
                <div class="form-group">
                    <label for="courseName">Course Name</label>
                    <input type="text" id="courseName" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="courseCode">Course Code (Optional)</label>
                    <input type="text" id="courseCode" name="code">
                </div>
                
                <div class="form-group">
                    <label for="courseColor">Color</label>
                    <input type="color" id="courseColor" name="color" value="#3498db">
                </div>
                
                <div class="form-group">
                    <label for="courseDescription">Description (Optional)</label>
                    <textarea id="courseDescription" name="description" rows="3"></textarea>
                </div>
            </form>
        </div>
        
        <div class="modal-footer">
            <button class="btn btn-secondary" onclick="closeModal('createCourseModal')">Cancel</button>
            <button class="btn btn-primary" onclick="createCourse()">Create Course</button>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
<script>
    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function() {
        initializeDashboard();
        loadImportantDates();
        checkAIStatus();
        setupFileDropZone();
    });
</script>
{% endblock %}
